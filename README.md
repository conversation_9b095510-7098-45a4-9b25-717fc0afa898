# 废土再生：回收者大作战 - Unity项目

## 项目概述

这是一个创新的消除+塔防+经营建造游戏，玩家在废土世界中通过消除废料块来净化环境，同时建造防御设施抵御污染生物的入侵。

## 技术栈

- **游戏引擎**: Unity 2022.3 LTS
- **编程语言**: C# (.NET Standard 2.1)
- **架构模式**: 事件驱动 + 服务定位器
- **UI系统**: Unity UI + TextMeshPro

## 项目结构

```
WastelandReclaim/
├── Scripts/
│   ├── Core/                    # 核心系统
│   │   ├── GameManager.cs       # 游戏总控制器
│   │   ├── EventSystem.cs       # 事件系统
│   │   └── GameInitializer.cs   # 游戏初始化器
│   ├── Data/                    # 数据模型
│   │   ├── DataModels.cs        # 数据结构定义
│   │   └── SaveSystem.cs        # 存档系统
│   ├── Gameplay/                # 游戏玩法
│   │   ├── GridSystem.cs        # 网格系统
│   │   ├── GridCell.cs          # 网格单元
│   │   ├── WasteBlock.cs        # 废料块
│   │   ├── MatchSystem.cs       # 消除系统
│   │   ├── TowerSystem.cs       # 塔防系统
│   │   ├── Tower.cs             # 防御塔
│   │   ├── EnemySystem.cs       # 敌人系统
│   │   └── Enemy.cs             # 敌人实体
│   ├── UI/                      # 用户界面
│   │   └── UIManager.cs         # UI管理器
│   └── Audio/                   # 音频系统
│       └── AudioManager.cs      # 音频管理器
├── Prefabs/                     # 预制体
├── Materials/                   # 材质
├── Textures/                    # 纹理
├── Audio/                       # 音频文件
└── Scenes/                      # 场景文件
```

## 快速开始

### 1. 环境要求

- Unity 2022.3 LTS 或更高版本
- Visual Studio 2022 或 Visual Studio Code
- .NET Framework 4.8 或更高版本

### 2. 项目设置

1. **创建新的Unity项目**
   - 打开Unity Hub
   - 点击"新建项目"
   - 选择"3D"模板
   - 项目名称：WastelandReclaim
   - 点击"创建项目"

2. **导入脚本文件**
   - 将所有Scripts文件夹中的脚本复制到Unity项目的Assets/Scripts/目录下
   - 确保文件夹结构与上述结构一致

3. **配置项目设置**
   ```
   Edit → Project Settings → Player
   - Company Name: YourCompanyName
   - Product Name: 废土再生：回收者大作战
   - Version: 1.0.0
   - Bundle Identifier: com.yourcompany.wastelandreclaim
   ```

### 3. 场景设置

1. **创建主场景**
   - 在Scenes文件夹中创建新场景：MainScene
   - 删除默认的Main Camera和Directional Light（GameInitializer会创建）

2. **设置GameInitializer**
   - 创建空的GameObject，命名为"GameInitializer"
   - 添加GameInitializer脚本组件
   - 配置相机和光照设置

3. **创建基础预制体**

   **网格单元预制体 (GridCell)**:
   ```
   - 创建Cube GameObject
   - 缩放到 (1, 0.1, 1)
   - 添加GridCell脚本
   - 设置材质为半透明白色
   - 保存为Prefab: GridCell
   ```

   **废料块预制体 (WasteBlock)**:
   ```
   - 创建Cube GameObject
   - 缩放到 (0.8, 0.8, 0.8)
   - 添加WasteBlock脚本
   - 为不同类型创建不同材质
   - 保存为Prefab: WasteBlock_Metal, WasteBlock_Plastic等
   ```

   **防御塔预制体 (Tower)**:
   ```
   - 创建Cylinder GameObject
   - 缩放到 (0.6, 1, 0.6)
   - 添加Tower脚本
   - 添加范围指示器（Sphere，半透明材质）
   - 保存为Prefab: Tower_WindFilter等
   ```

   **敌人预制体 (Enemy)**:
   ```
   - 创建Capsule GameObject
   - 缩放到 (0.5, 0.5, 0.5)
   - 添加Enemy脚本
   - 添加血条UI
   - 保存为Prefab: Enemy_Crawler等
   ```

### 4. UI设置

1. **创建Canvas**
   - 右键Hierarchy → UI → Canvas
   - 设置Canvas Scaler为"Scale With Screen Size"
   - Reference Resolution: 1920x1080

2. **创建UI面板**
   - MainMenuPanel: 主菜单界面
   - GameUIPanel: 游戏界面
   - PauseMenuPanel: 暂停菜单
   - VictoryPanel: 胜利界面
   - GameOverPanel: 失败界面

3. **配置UIManager**
   - 将创建的UI面板拖拽到UIManager的对应字段
   - 设置按钮和文本组件的引用

### 5. 音频设置

1. **创建音频文件夹结构**
   ```
   Audio/
   ├── Music/
   ├── SFX/
   ├── UI/
   └── Ambient/
   ```

2. **配置AudioManager**
   - 导入音频文件到对应文件夹
   - 在AudioManager中配置音频剪辑数组

## 核心系统说明

### 游戏管理器 (GameManager)

游戏的核心控制器，管理游戏状态、关卡进度和系统协调。

**主要功能**:
- 游戏状态管理（主菜单、游戏中、暂停等）
- 关卡加载和进度跟踪
- 资源管理和步数控制
- 胜负条件检查

### 网格系统 (GridSystem)

管理游戏网格、废料块放置和消除逻辑。

**主要功能**:
- 网格创建和可视化
- 废料块生成和管理
- 消除匹配检测
- 重力效果模拟

### 消除系统 (MatchSystem)

处理废料块的匹配和消除逻辑。

**主要功能**:
- 连续块匹配算法
- 连击系统
- 特殊效果触发
- 资源奖励计算

### 塔防系统 (TowerSystem)

管理防御塔的建造、升级和战斗。

**主要功能**:
- 防御塔建造和放置
- 塔的升级系统
- 目标选择和攻击逻辑
- 建造成本管理

### 敌人系统 (EnemySystem)

管理敌人的生成、移动和AI行为。

**主要功能**:
- 敌人波次管理
- 路径寻找和移动
- 状态效果处理
- 对象池优化

## 游戏玩法

### 基础玩法循环

1. **观察局面**: 查看当前网格上的废料块分布
2. **规划策略**: 决定消除顺序和防御塔建造位置
3. **执行消除**: 点击废料块进行消除，获得资源
4. **建造防御**: 使用资源建造防御塔
5. **抵御入侵**: 防御塔自动攻击入侵的敌人
6. **达成目标**: 将污染值降低到目标值以下

### 胜利条件

- 将关卡污染值降低到目标值以下
- 在步数限制内完成净化
- 保护净化装置不被敌人摧毁

### 失败条件

- 步数用完但污染值仍超过目标
- 时间限制到达但未完成净化
- 净化装置被敌人摧毁

## 开发指南

### 添加新的废料块类型

1. 在`WasteBlockType`枚举中添加新类型
2. 在`WasteBlock.cs`中添加对应的资源掉落逻辑
3. 创建对应的预制体和材质
4. 在`GameConfig`中配置掉落数据

### 添加新的防御塔

1. 在`TowerType`枚举中添加新类型
2. 在`Tower.cs`中实现对应的攻击逻辑
3. 创建预制体和特效
4. 在`GameConfig`中配置塔的属性和成本

### 添加新的敌人

1. 在`EnemyType`枚举中添加新类型
2. 在`Enemy.cs`中实现特殊行为
3. 创建预制体和动画
4. 在`GameConfig`中配置敌人属性

### 创建新关卡

1. 创建`LevelData`实例
2. 配置网格大小、污染值、目标等
3. 设置废料块分布和敌人波次
4. 测试平衡性和可玩性

## 性能优化

### 对象池

- 敌人使用对象池管理，避免频繁创建销毁
- 特效粒子系统复用
- UI元素按需激活

### 渲染优化

- 使用Sprite Atlas减少Draw Call
- 合理设置LOD距离
- 动态批处理相同材质对象

### 内存管理

- 及时释放不用的资源
- 音频文件压缩
- 纹理分辨率优化

## 测试指南

### 单元测试

- 核心逻辑算法测试
- 数据序列化测试
- 事件系统测试

### 集成测试

- 系统间交互测试
- 关卡完整流程测试
- 存档加载测试

### 性能测试

- 帧率稳定性测试
- 内存使用监控
- 电池消耗测试

## 发布准备

### 构建设置

1. **Player Settings**
   - 设置图标和启动画面
   - 配置权限和功能
   - 优化图形设置

2. **Build Settings**
   - 添加所有场景
   - 选择目标平台
   - 配置压缩和优化选项

### 平台特定设置

**Android**:
- 最低API Level: 21 (Android 5.0)
- Target API Level: 最新
- Scripting Backend: IL2CPP
- Architecture: ARM64

**iOS**:
- 最低版本: iOS 12.0
- Architecture: ARM64
- 代码签名配置

## 故障排除

### 常见问题

1. **脚本编译错误**
   - 检查命名空间是否正确
   - 确认所有依赖项已导入
   - 重新导入脚本文件

2. **预制体引用丢失**
   - 重新分配预制体引用
   - 检查预制体是否在正确路径
   - 确认组件配置正确

3. **性能问题**
   - 使用Profiler分析性能瓶颈
   - 检查对象池是否正常工作
   - 优化渲染设置

### 调试技巧

- 使用Debug.Log输出关键信息
- 利用Unity Profiler监控性能
- 在Scene视图中可视化调试信息
- 使用断点调试复杂逻辑

## 贡献指南

### 代码规范

- 使用C#命名约定
- 添加必要的注释
- 保持代码简洁易读
- 遵循SOLID原则

### 提交规范

- 清晰的提交信息
- 小而频繁的提交
- 功能分支开发
- 代码审查流程

---

**联系信息**
- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目地址: [GitHub Repository URL]

**许可证**
本项目采用 MIT 许可证，详见 LICENSE 文件。
