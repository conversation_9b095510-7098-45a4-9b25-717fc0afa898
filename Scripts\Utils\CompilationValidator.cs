using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 编译验证器 - 确保所有新功能正常工作
    /// </summary>
    public class CompilationValidator : MonoBehaviour
    {
        [Header("验证设置")]
        public bool runValidationOnStart = true;
        public bool showDetailedLog = true;

        private void Start()
        {
            if (runValidationOnStart)
            {
                ValidateAllSystems();
            }
        }

        [ContextMenu("验证所有系统")]
        public void ValidateAllSystems()
        {
            Debug.Log("=== 开始系统验证 ===");

            bool allValid = true;

            allValid &= ValidateEnums();
            allValid &= ValidateEventSystem();
            allValid &= ValidateDataModels();
            allValid &= ValidateMaterialGenerator();
            allValid &= ValidateGameplaySystems();

            if (allValid)
            {
                Debug.Log("✅ 所有系统验证通过！");
            }
            else
            {
                Debug.LogError("❌ 部分系统验证失败，请检查错误信息");
            }

            Debug.Log("=== 系统验证完成 ===");
        }

        private bool ValidateEnums()
        {
            if (showDetailedLog) Debug.Log("验证枚举定义...");

            try
            {
                // 验证所有枚举类型
                var resourceTypes = System.Enum.GetValues(typeof(ResourceType));
                var wasteBlockTypes = System.Enum.GetValues(typeof(WasteBlockType));
                var enemyTypes = System.Enum.GetValues(typeof(EnemyType));
                var towerTypes = System.Enum.GetValues(typeof(TowerType));
                var gameStates = System.Enum.GetValues(typeof(GameState));

                Debug.Log($"✅ 枚举验证通过: ResourceType({resourceTypes.Length}), WasteBlockType({wasteBlockTypes.Length}), EnemyType({enemyTypes.Length}), TowerType({towerTypes.Length}), GameState({gameStates.Length})");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ 枚举验证失败: {e.Message}");
                return false;
            }
        }

        private bool ValidateEventSystem()
        {
            if (showDetailedLog) Debug.Log("验证事件系统...");

            try
            {
                // 测试事件订阅和发布
                bool eventReceived = false;
                System.Action<TestEvent> handler = (e) => eventReceived = true;

                EventSystem.Subscribe<TestEvent>(handler);
                EventSystem.Publish(new TestEvent());
                EventSystem.Unsubscribe<TestEvent>(handler);

                if (eventReceived)
                {
                    Debug.Log("✅ 事件系统验证通过");
                    return true;
                }
                else
                {
                    Debug.LogError("❌ 事件系统验证失败: 事件未正确传递");
                    return false;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ 事件系统验证失败: {e.Message}");
                return false;
            }
        }

        private bool ValidateDataModels()
        {
            if (showDetailedLog) Debug.Log("验证数据模型...");

            try
            {
                // 测试LevelData创建
                var levelData = new LevelData
                {
                    levelId = 1,
                    levelName = "测试关卡",
                    gridSize = new Vector2Int(8, 8),
                    initialPollution = 100,
                    targetPollution = 10
                };

                // 测试perfectReward字段
                levelData.perfectReward[ResourceType.Metal] = 50;
                levelData.basicReward[ResourceType.Plastic] = 25;

                // 测试WasteBlockData创建
                var wasteBlockData = new WasteBlockData
                {
                    type = WasteBlockType.Metal,
                    position = new Vector2Int(0, 0),
                    pollutionValue = 2
                };

                Debug.Log("✅ 数据模型验证通过");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ 数据模型验证失败: {e.Message}");
                return false;
            }
        }

        private bool ValidateMaterialGenerator()
        {
            if (showDetailedLog) Debug.Log("验证材质生成器...");

            try
            {
                // 测试废料块材质生成
                var metalMaterial = MaterialGenerator.CreateWasteBlockMaterial(WasteBlockType.Metal);
                var plasticMaterial = MaterialGenerator.CreateWasteBlockMaterial(WasteBlockType.Plastic);

                // 测试防御塔材质生成
                var towerMaterial = MaterialGenerator.CreateTowerMaterial(TowerType.WindFilter);

                // 测试其他材质生成
                var gridMaterial = MaterialGenerator.CreateGridMaterial();
                var glowMaterial = MaterialGenerator.CreateGlowMaterial(Color.red, 1f);

                if (metalMaterial != null && plasticMaterial != null && towerMaterial != null && 
                    gridMaterial != null && glowMaterial != null)
                {
                    Debug.Log("✅ 材质生成器验证通过");
                    return true;
                }
                else
                {
                    Debug.LogError("❌ 材质生成器验证失败: 某些材质为null");
                    return false;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ 材质生成器验证失败: {e.Message}");
                return false;
            }
        }

        private bool ValidateGameplaySystems()
        {
            if (showDetailedLog) Debug.Log("验证游戏玩法系统...");

            try
            {
                // 验证特殊块类型
                bool hasSpecialBlocks = System.Enum.IsDefined(typeof(WasteBlockType), WasteBlockType.Bomb) &&
                                       System.Enum.IsDefined(typeof(WasteBlockType), WasteBlockType.LineHorizontal) &&
                                       System.Enum.IsDefined(typeof(WasteBlockType), WasteBlockType.LineVertical) &&
                                       System.Enum.IsDefined(typeof(WasteBlockType), WasteBlockType.ColorBomb);

                if (!hasSpecialBlocks)
                {
                    Debug.LogError("❌ 游戏玩法系统验证失败: 特殊块类型未定义");
                    return false;
                }

                // 验证事件类型
                var matchEvent = new MatchEvent
                {
                    matchedBlocks = new List<Vector2Int>(),
                    resources = new Dictionary<ResourceType, int>(),
                    matchType = WasteBlockType.Metal,
                    score = 100,
                    position = Vector3.zero
                };

                var scoreEvent = new ScoreEvent
                {
                    points = 100,
                    position = Vector3.zero
                };

                var resourceEvent = new ResourceGainEvent
                {
                    resources = new Dictionary<ResourceType, int>()
                };

                Debug.Log("✅ 游戏玩法系统验证通过");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ 游戏玩法系统验证失败: {e.Message}");
                return false;
            }
        }

        [ContextMenu("测试事件系统")]
        public void TestEventSystem()
        {
            Debug.Log("测试事件系统...");

            // 测试各种事件类型
            EventSystem.Publish(new MatchEvent
            {
                matchedBlocks = new List<Vector2Int> { new Vector2Int(0, 0) },
                matchType = WasteBlockType.Metal,
                score = 100,
                resources = new Dictionary<ResourceType, int> { { ResourceType.Metal, 5 } },
                position = Vector3.zero
            });

            EventSystem.Publish(new ScoreEvent
            {
                points = 100,
                position = Vector3.zero
            });

            EventSystem.Publish(new ComboEvent
            {
                comboCount = 3,
                multiplier = 1.5f
            });

            Debug.Log("事件系统测试完成");
        }

        [ContextMenu("测试材质生成")]
        public void TestMaterialGeneration()
        {
            Debug.Log("测试材质生成...");

            // 测试所有废料块类型的材质
            foreach (WasteBlockType wasteType in System.Enum.GetValues(typeof(WasteBlockType)))
            {
                var material = MaterialGenerator.CreateWasteBlockMaterial(wasteType);
                Debug.Log($"生成材质: {wasteType} -> {(material != null ? "成功" : "失败")}");
            }

            // 测试所有防御塔类型的材质
            foreach (TowerType towerType in System.Enum.GetValues(typeof(TowerType)))
            {
                var material = MaterialGenerator.CreateTowerMaterial(towerType);
                Debug.Log($"生成塔材质: {towerType} -> {(material != null ? "成功" : "失败")}");
            }

            Debug.Log("材质生成测试完成");
        }

        [ContextMenu("显示系统信息")]
        public void ShowSystemInfo()
        {
            Debug.Log("=== 系统信息 ===");
            Debug.Log($"Unity版本: {Application.unityVersion}");
            Debug.Log($"平台: {Application.platform}");
            Debug.Log($"渲染管线: {UnityEngine.Rendering.GraphicsSettings.renderPipelineAsset?.GetType().Name ?? "Built-in"}");
            
            Debug.Log("\n=== 枚举统计 ===");
            Debug.Log($"ResourceType: {System.Enum.GetValues(typeof(ResourceType)).Length} 种");
            Debug.Log($"WasteBlockType: {System.Enum.GetValues(typeof(WasteBlockType)).Length} 种");
            Debug.Log($"EnemyType: {System.Enum.GetValues(typeof(EnemyType)).Length} 种");
            Debug.Log($"TowerType: {System.Enum.GetValues(typeof(TowerType)).Length} 种");
            Debug.Log($"GameState: {System.Enum.GetValues(typeof(GameState)).Length} 种");

            Debug.Log("\n=== 事件系统统计 ===");
            Debug.Log($"MatchEvent订阅者: {EventSystem.GetSubscriberCount<MatchEvent>()}");
            Debug.Log($"ScoreEvent订阅者: {EventSystem.GetSubscriberCount<ScoreEvent>()}");
            Debug.Log($"ComboEvent订阅者: {EventSystem.GetSubscriberCount<ComboEvent>()}");
        }
    }

    // 测试事件类型
    public class TestEvent : GameEvent { }
}
