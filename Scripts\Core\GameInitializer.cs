using System.Collections;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 游戏初始化器 - 负责设置和启动所有游戏系统
    /// 这个脚本应该附加到场景中的一个GameObject上
    /// </summary>
    public class GameInitializer : MonoBehaviour
    {
        [Header("System Prefabs")]
        public GameObject gameManagerPrefab;
        public GameObject uiManagerPrefab;
        public GameObject audioManagerPrefab;
        public GameObject gridSystemPrefab;
        public GameObject towerSystemPrefab;
        public GameObject enemySystemPrefab;
        public GameObject matchSystemPrefab;

        [Header("Camera Settings")]
        public Camera mainCamera;
        public Vector3 cameraPosition = new Vector3(4, 10, -8);
        public Vector3 cameraRotation = new Vector3(45, 0, 0);

        [Header("Lighting")]
        public Light directionalLight;
        public Color lightColor = Color.white;
        public float lightIntensity = 1f;

        [Header("Environment")]
        public GameObject environmentParent;
        public Material skyboxMaterial;

        private bool isInitialized = false;

        private void Awake()
        {
            // Ensure this runs before any other scripts
            if (!isInitialized)
            {
                StartCoroutine(InitializeGame());
            }
        }

        private IEnumerator InitializeGame()
        {
            Debug.Log("Starting game initialization...");

            // Set up basic scene
            SetupCamera();
            SetupLighting();
            SetupEnvironment();

            yield return null; // Wait one frame

            // Initialize core systems in order
            yield return StartCoroutine(InitializeCoreSystem());
            yield return StartCoroutine(InitializeGameplaySystems());
            yield return StartCoroutine(InitializeUISystems());
            yield return StartCoroutine(InitializeAudioSystems());

            // Final setup
            yield return StartCoroutine(FinalizeInitialization());

            isInitialized = true;
            Debug.Log("Game initialization complete!");
        }

        private void SetupCamera()
        {
            if (mainCamera == null)
            {
                mainCamera = Camera.main;
                if (mainCamera == null)
                {
                    GameObject cameraObj = new GameObject("Main Camera");
                    mainCamera = cameraObj.AddComponent<Camera>();
                    cameraObj.tag = "MainCamera";
                }
            }

            mainCamera.transform.position = cameraPosition;
            mainCamera.transform.eulerAngles = cameraRotation;
            mainCamera.clearFlags = CameraClearFlags.Skybox;
            mainCamera.fieldOfView = 60f;
            mainCamera.nearClipPlane = 0.3f;
            mainCamera.farClipPlane = 1000f;

            // Add camera controller if needed
            CameraController cameraController = mainCamera.GetComponent<CameraController>();
            if (cameraController == null)
            {
                cameraController = mainCamera.gameObject.AddComponent<CameraController>();
            }
        }

        private void SetupLighting()
        {
            if (directionalLight == null)
            {
                GameObject lightObj = new GameObject("Directional Light");
                directionalLight = lightObj.AddComponent<Light>();
            }

            directionalLight.type = LightType.Directional;
            directionalLight.color = lightColor;
            directionalLight.intensity = lightIntensity;
            directionalLight.shadows = LightShadows.Soft;
            directionalLight.transform.rotation = Quaternion.Euler(50f, -30f, 0f);

            // Set ambient lighting
            RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
            RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f);
            RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f);
            RenderSettings.ambientGroundColor = new Color(0.2f, 0.2f, 0.2f);

            // Set skybox
            if (skyboxMaterial != null)
            {
                RenderSettings.skybox = skyboxMaterial;
            }
        }

        private void SetupEnvironment()
        {
            if (environmentParent == null)
            {
                environmentParent = new GameObject("Environment");
            }

            // Create ground plane
            CreateGroundPlane();

            // Set up physics
            Physics.gravity = new Vector3(0, -9.81f, 0);
        }

        private void CreateGroundPlane()
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.SetParent(environmentParent.transform);
            ground.transform.position = new Vector3(4, -0.5f, 4);
            ground.transform.localScale = new Vector3(2, 1, 2);

            // Set ground material
            Renderer groundRenderer = ground.GetComponent<Renderer>();
            if (groundRenderer != null)
            {
                Material groundMaterial = new Material(Shader.Find("Standard"));
                groundMaterial.color = new Color(0.4f, 0.3f, 0.2f); // Brown wasteland color
                groundRenderer.material = groundMaterial;
            }
        }

        private IEnumerator InitializeCoreSystem()
        {
            Debug.Log("Initializing core systems...");

            // Create GameManager
            if (GameManager.Instance == null)
            {
                GameObject gameManagerObj;
                if (gameManagerPrefab != null)
                {
                    gameManagerObj = Instantiate(gameManagerPrefab);
                }
                else
                {
                    gameManagerObj = new GameObject("GameManager");
                    gameManagerObj.AddComponent<GameManager>();
                }
                gameManagerObj.name = "GameManager";
            }

            yield return new WaitForSeconds(0.1f);
        }

        private IEnumerator InitializeGameplaySystems()
        {
            Debug.Log("Initializing gameplay systems...");

            // Create GridSystem
            GameObject gridSystemObj;
            if (gridSystemPrefab != null)
            {
                gridSystemObj = Instantiate(gridSystemPrefab);
            }
            else
            {
                gridSystemObj = new GameObject("GridSystem");
                gridSystemObj.AddComponent<GridSystem>();
            }
            gridSystemObj.name = "GridSystem";

            yield return new WaitForSeconds(0.1f);

            // Create MatchSystem
            GameObject matchSystemObj;
            if (matchSystemPrefab != null)
            {
                matchSystemObj = Instantiate(matchSystemPrefab);
            }
            else
            {
                matchSystemObj = new GameObject("MatchSystem");
                matchSystemObj.AddComponent<MatchSystem>();
            }
            matchSystemObj.name = "MatchSystem";

            yield return new WaitForSeconds(0.1f);

            // Create TowerSystem
            GameObject towerSystemObj;
            if (towerSystemPrefab != null)
            {
                towerSystemObj = Instantiate(towerSystemPrefab);
            }
            else
            {
                towerSystemObj = new GameObject("TowerSystem");
                towerSystemObj.AddComponent<TowerSystem>();
            }
            towerSystemObj.name = "TowerSystem";

            yield return new WaitForSeconds(0.1f);

            // Create EnemySystem
            GameObject enemySystemObj;
            if (enemySystemPrefab != null)
            {
                enemySystemObj = Instantiate(enemySystemPrefab);
            }
            else
            {
                enemySystemObj = new GameObject("EnemySystem");
                enemySystemObj.AddComponent<EnemySystem>();
            }
            enemySystemObj.name = "EnemySystem";

            yield return new WaitForSeconds(0.1f);
        }

        private IEnumerator InitializeUISystems()
        {
            Debug.Log("Initializing UI systems...");

            // Create UIManager
            GameObject uiManagerObj;
            if (uiManagerPrefab != null)
            {
                uiManagerObj = Instantiate(uiManagerPrefab);
            }
            else
            {
                uiManagerObj = new GameObject("UIManager");
                uiManagerObj.AddComponent<UIManager>();
            }
            uiManagerObj.name = "UIManager";

            yield return new WaitForSeconds(0.1f);
        }

        private IEnumerator InitializeAudioSystems()
        {
            Debug.Log("Initializing audio systems...");

            // Create AudioManager
            GameObject audioManagerObj;
            if (audioManagerPrefab != null)
            {
                audioManagerObj = Instantiate(audioManagerPrefab);
            }
            else
            {
                audioManagerObj = new GameObject("AudioManager");
                audioManagerObj.AddComponent<AudioManager>();
            }
            audioManagerObj.name = "AudioManager";

            yield return new WaitForSeconds(0.1f);
        }

        private IEnumerator FinalizeInitialization()
        {
            Debug.Log("Finalizing initialization...");

            // Ensure all systems are properly connected
            ConnectSystems();

            // Load player data
            LoadGameData();

            // Set initial game state
            if (GameManager.Instance != null)
            {
                GameManager.Instance.ChangeGameState(GameState.MainMenu);
            }

            yield return new WaitForSeconds(0.1f);
        }

        private void ConnectSystems()
        {
            // Find and connect all systems
            GridSystem gridSystem = FindObjectOfType<GridSystem>();
            MatchSystem matchSystem = FindObjectOfType<MatchSystem>();
            TowerSystem towerSystem = FindObjectOfType<TowerSystem>();
            EnemySystem enemySystem = FindObjectOfType<EnemySystem>();
            UIManager uiManager = FindObjectOfType<UIManager>();
            AudioManager audioManager = FindObjectOfType<AudioManager>();

            // Connect systems to GameManager
            if (GameManager.Instance != null)
            {
                GameManager.Instance.gridSystem = gridSystem;
                GameManager.Instance.matchSystem = matchSystem;
                GameManager.Instance.towerSystem = towerSystem;
                GameManager.Instance.enemySystem = enemySystem;
                GameManager.Instance.uiManager = uiManager;
                GameManager.Instance.audioManager = audioManager;
            }

            // Initialize systems
            if (gridSystem != null) gridSystem.Initialize();
            if (matchSystem != null) matchSystem.Initialize();
            if (towerSystem != null) towerSystem.Initialize();
            if (enemySystem != null) enemySystem.Initialize();
            if (uiManager != null) uiManager.Initialize();
            if (audioManager != null) audioManager.Initialize();
        }

        private void LoadGameData()
        {
            // Load player data
            PlayerData playerData = SaveSystem.LoadPlayerData();
            if (GameManager.Instance != null)
            {
                GameManager.Instance.playerData = playerData;
            }

            Debug.Log($"Loaded player data for: {playerData.playerName}");
        }

        // Public methods for manual initialization (useful for testing)
        public void ForceReinitialize()
        {
            isInitialized = false;
            StartCoroutine(InitializeGame());
        }

        public bool IsInitialized()
        {
            return isInitialized;
        }

        private void OnValidate()
        {
            // Validate settings in editor
            if (cameraPosition.y < 1f)
            {
                cameraPosition.y = 1f;
            }

            if (lightIntensity < 0f)
            {
                lightIntensity = 0f;
            }
        }
    }

    /// <summary>
    /// 简单的相机控制器
    /// </summary>
    public class CameraController : MonoBehaviour
    {
        [Header("Camera Control")]
        public bool enableMouseControl = true;
        public float mouseSensitivity = 2f;
        public float zoomSpeed = 2f;
        public float minZoom = 3f;
        public float maxZoom = 15f;

        private Vector3 lastMousePosition;
        private Camera cam;

        private void Start()
        {
            cam = GetComponent<Camera>();
        }

        private void Update()
        {
            if (!enableMouseControl) return;

            HandleMouseInput();
            HandleZoom();
        }

        private void HandleMouseInput()
        {
            if (Input.GetMouseButtonDown(1)) // Right mouse button
            {
                lastMousePosition = Input.mousePosition;
            }

            if (Input.GetMouseButton(1))
            {
                Vector3 deltaMousePosition = Input.mousePosition - lastMousePosition;
                
                // Rotate camera around target
                transform.Rotate(Vector3.up, deltaMousePosition.x * mouseSensitivity, Space.World);
                transform.Rotate(Vector3.right, -deltaMousePosition.y * mouseSensitivity, Space.Self);
                
                lastMousePosition = Input.mousePosition;
            }
        }

        private void HandleZoom()
        {
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (scroll != 0f)
            {
                Vector3 position = transform.position;
                Vector3 forward = transform.forward;
                
                position += forward * scroll * zoomSpeed;
                
                // Clamp zoom distance
                float distance = Vector3.Distance(position, Vector3.zero);
                if (distance >= minZoom && distance <= maxZoom)
                {
                    transform.position = position;
                }
            }
        }
    }
}
