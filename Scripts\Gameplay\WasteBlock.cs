using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    public class WasteBlock : MonoBehaviour
    {
        [Header("Block Data")]
        public WasteBlockType blockType;
        public int pollutionValue = 2;
        public Vector2Int gridPosition;

        [Header("Visual")]
        public Renderer blockRenderer;
        public ParticleSystem destructionEffect;
        public GameObject selectionHighlight;

        [Header("Animation")]
        public float hoverHeight = 0.1f;
        public float hoverSpeed = 2f;
        public float rotationSpeed = 30f;

        private Vector3 originalPosition;
        private bool isSelected = false;
        private bool isHovered = false;
        private WasteBlockData blockData;
        private Dictionary<ResourceType, int> resourceDrops;

        private void Start()
        {
            originalPosition = transform.position;
            InitializeResourceDrops();
            SetupVisuals();
        }

        private void Update()
        {
            UpdateAnimations();
        }

        public void Initialize(WasteBlockData data)
        {
            blockData = data;
            blockType = data.type;
            pollutionValue = data.pollutionValue;
            gridPosition = data.position;
            
            InitializeResourceDrops();
            SetupVisuals();
        }

        private void InitializeResourceDrops()
        {
            resourceDrops = new Dictionary<ResourceType, int>();
            
            switch (blockType)
            {
                case WasteBlockType.Metal:
                    resourceDrops[ResourceType.Metal] = Random.Range(2, 5);
                    break;
                case WasteBlockType.Plastic:
                    resourceDrops[ResourceType.Plastic] = Random.Range(1, 4);
                    break;
                case WasteBlockType.Wood:
                    resourceDrops[ResourceType.Wood] = Random.Range(3, 6);
                    break;
                case WasteBlockType.Electronic:
                    resourceDrops[ResourceType.Electronic] = 1;
                    resourceDrops[ResourceType.Metal] = Random.Range(1, 3);
                    break;
                case WasteBlockType.Glass:
                    resourceDrops[ResourceType.Glass] = Random.Range(1, 3);
                    break;
                case WasteBlockType.Organic:
                    resourceDrops[ResourceType.Organic] = Random.Range(2, 5);
                    break;
                case WasteBlockType.PollutionCore:
                    resourceDrops[ResourceType.CoreFragment] = Random.Range(3, 6);
                    pollutionValue = 10;
                    break;
                case WasteBlockType.RegenerationCore:
                    resourceDrops[ResourceType.Core] = 1;
                    pollutionValue = -5; // Negative pollution (healing)
                    break;
                case WasteBlockType.MutatedWaste:
                    resourceDrops[ResourceType.Metal] = Random.Range(1, 3);
                    resourceDrops[ResourceType.Plastic] = Random.Range(1, 3);
                    pollutionValue = 5;
                    break;
                case WasteBlockType.FrozenWaste:
                    // Requires multiple hits to break
                    resourceDrops[ResourceType.Metal] = Random.Range(3, 6);
                    pollutionValue = 3;
                    break;
                case WasteBlockType.ExplosiveWaste:
                    resourceDrops[ResourceType.Electronic] = Random.Range(1, 3);
                    pollutionValue = 8;
                    break;
            }

            // Random chance for bonus resources
            if (Random.Range(0f, 1f) < 0.1f) // 10% chance
            {
                resourceDrops[ResourceType.CoreFragment] = (resourceDrops.ContainsKey(ResourceType.CoreFragment) ? resourceDrops[ResourceType.CoreFragment] : 0) + 1;
            }

            if (Random.Range(0f, 1f) < 0.01f) // 1% chance
            {
                resourceDrops[ResourceType.Core] = (resourceDrops.ContainsKey(ResourceType.Core) ? resourceDrops[ResourceType.Core] : 0) + 1;
            }
        }

        private void SetupVisuals()
        {
            if (blockRenderer == null)
                blockRenderer = GetComponent<Renderer>();

            if (blockRenderer != null)
            {
                Color blockColor = GetBlockColor();
                blockRenderer.material.color = blockColor;
            }

            // Setup selection highlight
            if (selectionHighlight != null)
            {
                selectionHighlight.SetActive(false);
            }
        }

        private Color GetBlockColor()
        {
            switch (blockType)
            {
                case WasteBlockType.Metal:
                    return new Color(0.7f, 0.7f, 0.8f); // Silver
                case WasteBlockType.Plastic:
                    return new Color(0.2f, 0.6f, 1f); // Blue
                case WasteBlockType.Wood:
                    return new Color(0.6f, 0.4f, 0.2f); // Brown
                case WasteBlockType.Electronic:
                    return new Color(0.2f, 0.8f, 0.2f); // Green
                case WasteBlockType.Glass:
                    return new Color(0.9f, 0.9f, 0.9f, 0.7f); // Transparent
                case WasteBlockType.Organic:
                    return new Color(1f, 0.8f, 0.2f); // Yellow
                case WasteBlockType.PollutionCore:
                    return new Color(0.2f, 0.1f, 0.1f); // Dark red
                case WasteBlockType.RegenerationCore:
                    return new Color(0.8f, 0.2f, 1f); // Purple
                case WasteBlockType.MutatedWaste:
                    return new Color(0.6f, 0.2f, 0.8f); // Purple
                case WasteBlockType.FrozenWaste:
                    return new Color(0.7f, 0.9f, 1f); // Light blue
                case WasteBlockType.ExplosiveWaste:
                    return new Color(1f, 0.3f, 0.1f); // Orange-red
                default:
                    return Color.white;
            }
        }

        private void UpdateAnimations()
        {
            // Hover animation
            if (isHovered || isSelected)
            {
                float hoverOffset = Mathf.Sin(Time.time * hoverSpeed) * hoverHeight;
                transform.position = originalPosition + Vector3.up * hoverOffset;
            }
            else
            {
                transform.position = Vector3.Lerp(transform.position, originalPosition, Time.deltaTime * 5f);
            }

            // Rotation animation for special blocks
            if (blockType == WasteBlockType.PollutionCore || blockType == WasteBlockType.RegenerationCore)
            {
                transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
            }

            // Pulsing effect for explosive waste
            if (blockType == WasteBlockType.ExplosiveWaste)
            {
                float scale = 1f + Mathf.Sin(Time.time * 4f) * 0.1f;
                transform.localScale = Vector3.one * scale;
            }
        }

        public Dictionary<ResourceType, int> GetResources()
        {
            return new Dictionary<ResourceType, int>(resourceDrops);
        }

        public void SetSelected(bool selected)
        {
            isSelected = selected;
            if (selectionHighlight != null)
            {
                selectionHighlight.SetActive(selected);
            }
        }

        public void SetHovered(bool hovered)
        {
            isHovered = hovered;
        }

        public bool CanBeMatched()
        {
            switch (blockType)
            {
                case WasteBlockType.FrozenWaste:
                    // Frozen waste needs to be "thawed" first
                    return false;
                default:
                    return true;
            }
        }

        public void OnDestroyed()
        {
            // Play destruction effect
            if (destructionEffect != null)
            {
                ParticleSystem effect = Instantiate(destructionEffect, transform.position, Quaternion.identity);
                effect.Play();
                Destroy(effect.gameObject, 2f);
            }

            // Special effects for certain block types
            switch (blockType)
            {
                case WasteBlockType.ExplosiveWaste:
                    TriggerExplosion();
                    break;
                case WasteBlockType.PollutionCore:
                    TriggerPollutionSpread();
                    break;
                case WasteBlockType.RegenerationCore:
                    TriggerRegeneration();
                    break;
            }
        }

        private void TriggerExplosion()
        {
            // Affect surrounding 3x3 area
            List<Vector2Int> affectedPositions = new List<Vector2Int>();
            
            for (int x = -1; x <= 1; x++)
            {
                for (int y = -1; y <= 1; y++)
                {
                    Vector2Int pos = gridPosition + new Vector2Int(x, y);
                    affectedPositions.Add(pos);
                }
            }

            EventSystem.Publish(new SpecialEffectEvent
            {
                effectType = SpecialEffectType.MassDestruction,
                position = gridPosition,
                affectedPositions = affectedPositions
            });
        }

        private void TriggerPollutionSpread()
        {
            // Increase pollution in surrounding area
            List<Vector2Int> affectedPositions = GameUtils.GetPositionsInRange(gridPosition, 2, 8, 8);

            EventSystem.Publish(new SpecialEffectEvent
            {
                effectType = SpecialEffectType.ChainPurification,
                position = gridPosition,
                affectedPositions = affectedPositions
            });
        }

        private void TriggerRegeneration()
        {
            // Heal pollution in surrounding area
            List<Vector2Int> affectedPositions = GameUtils.GetPositionsInRange(gridPosition, 3, 8, 8);

            EventSystem.Publish(new SpecialEffectEvent
            {
                effectType = SpecialEffectType.Regeneration,
                position = gridPosition,
                affectedPositions = affectedPositions
            });
        }

        public bool IsSpecialBlock()
        {
            return blockType == WasteBlockType.PollutionCore ||
                   blockType == WasteBlockType.RegenerationCore ||
                   blockType == WasteBlockType.ExplosiveWaste ||
                   blockType == WasteBlockType.MutatedWaste ||
                   blockType == WasteBlockType.FrozenWaste;
        }

        public string GetBlockDescription()
        {
            switch (blockType)
            {
                case WasteBlockType.Metal:
                    return "金属废料 - 提供金属资源";
                case WasteBlockType.Plastic:
                    return "塑料废料 - 提供塑料资源";
                case WasteBlockType.Wood:
                    return "木材废料 - 提供生物质燃料";
                case WasteBlockType.Electronic:
                    return "电子废料 - 提供稀有电子元件";
                case WasteBlockType.Glass:
                    return "玻璃废料 - 提供硅材料";
                case WasteBlockType.Organic:
                    return "有机废料 - 提供肥料";
                case WasteBlockType.PollutionCore:
                    return "污染核心 - 高污染值，但提供大量核心碎片";
                case WasteBlockType.RegenerationCore:
                    return "再生核心 - 减少污染，提供完整核心";
                case WasteBlockType.MutatedWaste:
                    return "变异废料 - 会自动移动的特殊废料";
                case WasteBlockType.FrozenWaste:
                    return "冰冻废料 - 需要连续消除才能激活";
                case WasteBlockType.ExplosiveWaste:
                    return "爆炸废料 - 消除时影响周围3x3区域";
                default:
                    return "未知废料";
            }
        }

        private void OnMouseEnter()
        {
            SetHovered(true);
        }

        private void OnMouseExit()
        {
            SetHovered(false);
        }

        private void OnMouseDown()
        {
            if (GameManager.Instance != null && GameManager.Instance.currentState == GameState.Playing)
            {
                GridSystem gridSystem = FindObjectOfType<GridSystem>();
                if (gridSystem != null)
                {
                    gridSystem.OnCellClicked(gridPosition);
                }
            }
        }
    }
}
