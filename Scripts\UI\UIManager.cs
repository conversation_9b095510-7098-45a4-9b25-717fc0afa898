using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace WastelandReclaim
{
    public class UIManager : MonoBehaviour
    {
        [Header("UI Panels")]
        public GameObject mainMenuPanel;
        public GameObject gameUIPanel;
        public GameObject pauseMenuPanel;
        public GameObject gameOverPanel;
        public GameObject victoryPanel;
        public GameObject baseBuildingPanel;
        public GameObject settingsPanel;
        public GameObject shopPanel;

        [<PERSON>er("Game UI Elements")]
        public TextMeshProUGUI pollutionText;
        public TextMeshProUGUI stepsText;
        public TextMeshProUGUI timeText;
        public TextMeshProUGUI[] resourceTexts;
        public Slider pollutionSlider;
        public Button pauseButton;
        public Button[] towerButtons;

        [Header("Main Menu Elements")]
        public Button startGameButton;
        public Button baseBuildingButton;
        public Button shopButton;
        public Button settingsButton;
        public Button exitButton;

        [Header("Pause Menu Elements")]
        public Button resumeButton;
        public Button restartButton;
        public Button mainMenuButton;

        [Header("Victory/Game Over Elements")]
        public TextMeshProUGUI resultText;
        public TextMeshProUGUI rewardText;
        public Button nextLevelButton;
        public Button retryButton;
        public Button backToMenuButton;

        [Header("Tower Building UI")]
        public GameObject towerBuildPanel;
        public Button[] buildTowerButtons;
        public TextMeshProUGUI towerInfoText;
        public TextMeshProUGUI towerCostText;

        private TowerSystem towerSystem;
        private bool isTowerBuildMode = false;

        public void Initialize()
        {
            towerSystem = FindObjectOfType<TowerSystem>();
            
            SetupButtonListeners();
            SetupEventSubscriptions();
            
            // Initialize all panels as inactive
            HideAllPanels();
        }

        private void SetupButtonListeners()
        {
            // Main Menu
            if (startGameButton != null)
                startGameButton.onClick.AddListener(OnStartGameClicked);
            if (baseBuildingButton != null)
                baseBuildingButton.onClick.AddListener(OnBaseBuildingClicked);
            if (shopButton != null)
                shopButton.onClick.AddListener(OnShopClicked);
            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);
            if (exitButton != null)
                exitButton.onClick.AddListener(OnExitClicked);

            // Game UI
            if (pauseButton != null)
                pauseButton.onClick.AddListener(OnPauseClicked);

            // Pause Menu
            if (resumeButton != null)
                resumeButton.onClick.AddListener(OnResumeClicked);
            if (restartButton != null)
                restartButton.onClick.AddListener(OnRestartClicked);
            if (mainMenuButton != null)
                mainMenuButton.onClick.AddListener(OnMainMenuClicked);

            // Victory/Game Over
            if (nextLevelButton != null)
                nextLevelButton.onClick.AddListener(OnNextLevelClicked);
            if (retryButton != null)
                retryButton.onClick.AddListener(OnRetryClicked);
            if (backToMenuButton != null)
                backToMenuButton.onClick.AddListener(OnBackToMenuClicked);

            // Tower Building
            SetupTowerButtons();
        }

        private void SetupTowerButtons()
        {
            if (towerButtons != null)
            {
                for (int i = 0; i < towerButtons.Length; i++)
                {
                    int towerIndex = i; // Capture for closure
                    if (towerButtons[i] != null)
                    {
                        towerButtons[i].onClick.AddListener(() => OnTowerButtonClicked(towerIndex));
                    }
                }
            }

            if (buildTowerButtons != null)
            {
                for (int i = 0; i < buildTowerButtons.Length; i++)
                {
                    int towerIndex = i; // Capture for closure
                    if (buildTowerButtons[i] != null)
                    {
                        buildTowerButtons[i].onClick.AddListener(() => OnBuildTowerClicked(towerIndex));
                    }
                }
            }
        }

        private void SetupEventSubscriptions()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged += OnGameStateChanged;
                GameManager.Instance.OnStepsChanged += OnStepsChanged;
                GameManager.Instance.OnTimeChanged += OnTimeChanged;
                GameManager.Instance.OnPollutionChanged += OnPollutionChanged;
                GameManager.Instance.OnResourcesChanged += OnResourcesChanged;
            }
        }

        private void HideAllPanels()
        {
            if (mainMenuPanel != null) mainMenuPanel.SetActive(false);
            if (gameUIPanel != null) gameUIPanel.SetActive(false);
            if (pauseMenuPanel != null) pauseMenuPanel.SetActive(false);
            if (gameOverPanel != null) gameOverPanel.SetActive(false);
            if (victoryPanel != null) victoryPanel.SetActive(false);
            if (baseBuildingPanel != null) baseBuildingPanel.SetActive(false);
            if (settingsPanel != null) settingsPanel.SetActive(false);
            if (shopPanel != null) shopPanel.SetActive(false);
            if (towerBuildPanel != null) towerBuildPanel.SetActive(false);
        }

        public void ShowMainMenu()
        {
            HideAllPanels();
            if (mainMenuPanel != null)
                mainMenuPanel.SetActive(true);
        }

        public void ShowGameUI()
        {
            HideAllPanels();
            if (gameUIPanel != null)
                gameUIPanel.SetActive(true);
            
            UpdateGameUI();
        }

        public void ShowPauseMenu()
        {
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);
        }

        public void ShowGameOverScreen()
        {
            HideAllPanels();
            if (gameOverPanel != null)
            {
                gameOverPanel.SetActive(true);
                if (resultText != null)
                    resultText.text = "游戏失败";
            }
        }

        public void ShowVictoryScreen()
        {
            HideAllPanels();
            if (victoryPanel != null)
            {
                victoryPanel.SetActive(true);
                if (resultText != null)
                    resultText.text = "净化成功！";
                
                // Show rewards
                UpdateRewardDisplay();
            }
        }

        public void ShowBaseBuilding()
        {
            HideAllPanels();
            if (baseBuildingPanel != null)
                baseBuildingPanel.SetActive(true);
        }

        private void UpdateGameUI()
        {
            // This will be called by event handlers
            // Initial setup here if needed
        }

        private void OnGameStateChanged(GameState newState)
        {
            switch (newState)
            {
                case GameState.MainMenu:
                    ShowMainMenu();
                    break;
                case GameState.Playing:
                    ShowGameUI();
                    break;
                case GameState.Paused:
                    ShowPauseMenu();
                    break;
                case GameState.GameOver:
                    ShowGameOverScreen();
                    break;
                case GameState.Victory:
                    ShowVictoryScreen();
                    break;
                case GameState.BaseBuilding:
                    ShowBaseBuilding();
                    break;
            }
        }

        private void OnStepsChanged(int currentSteps)
        {
            if (stepsText != null)
            {
                int maxSteps = GameManager.Instance.maxSteps;
                stepsText.text = $"步数: {currentSteps}/{maxSteps}";
                
                // Change color if running low
                if (currentSteps > maxSteps * 0.8f)
                {
                    stepsText.color = Color.red;
                }
                else if (currentSteps > maxSteps * 0.6f)
                {
                    stepsText.color = Color.yellow;
                }
                else
                {
                    stepsText.color = Color.white;
                }
            }
        }

        private void OnTimeChanged(float currentTime)
        {
            if (timeText != null)
            {
                timeText.text = $"时间: {GameUtils.FormatTime(currentTime)}";
            }
        }

        private void OnPollutionChanged(int currentPollution)
        {
            if (pollutionText != null)
            {
                int targetPollution = GameManager.Instance.targetPollution;
                pollutionText.text = $"污染值: {currentPollution}/{targetPollution}";
            }

            if (pollutionSlider != null)
            {
                int maxPollution = GameManager.Instance.currentLevel?.initialPollution ?? 100;
                pollutionSlider.value = (float)currentPollution / maxPollution;
                
                // Change slider color based on pollution level
                Image fillImage = pollutionSlider.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    if (pollutionSlider.value > 0.7f)
                        fillImage.color = Color.red;
                    else if (pollutionSlider.value > 0.4f)
                        fillImage.color = Color.yellow;
                    else
                        fillImage.color = Color.green;
                }
            }
        }

        private void OnResourcesChanged(Dictionary<ResourceType, int> resources)
        {
            if (resourceTexts != null)
            {
                int index = 0;
                foreach (var kvp in resources)
                {
                    if (index < resourceTexts.Length && resourceTexts[index] != null)
                    {
                        resourceTexts[index].text = $"{kvp.Key}: {GameUtils.FormatNumber(kvp.Value)}";
                        resourceTexts[index].color = GameUtils.GetResourceColor(kvp.Key);
                        index++;
                    }
                }
            }
        }

        private void UpdateRewardDisplay()
        {
            if (rewardText != null)
            {
                // Calculate and display rewards
                string rewardString = "获得奖励:\n";
                rewardString += "经验值: +100\n";
                rewardString += "金属: +20\n";
                rewardString += "塑料: +15\n";
                rewardText.text = rewardString;
            }
        }

        // Button Event Handlers
        private void OnStartGameClicked()
        {
            // Load level selection or start first level
            LevelData testLevel = CreateTestLevel();
            GameManager.Instance.StartLevel(testLevel);
        }

        private void OnBaseBuildingClicked()
        {
            GameManager.Instance.GoToBaseBuilding();
        }

        private void OnShopClicked()
        {
            if (shopPanel != null)
            {
                shopPanel.SetActive(!shopPanel.activeInHierarchy);
            }
        }

        private void OnSettingsClicked()
        {
            if (settingsPanel != null)
            {
                settingsPanel.SetActive(!settingsPanel.activeInHierarchy);
            }
        }

        private void OnExitClicked()
        {
            Application.Quit();
        }

        private void OnPauseClicked()
        {
            GameManager.Instance.PauseGame();
        }

        private void OnResumeClicked()
        {
            GameManager.Instance.ResumeGame();
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(false);
        }

        private void OnRestartClicked()
        {
            GameManager.Instance.RestartLevel();
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(false);
        }

        private void OnMainMenuClicked()
        {
            GameManager.Instance.ReturnToMainMenu();
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(false);
        }

        private void OnNextLevelClicked()
        {
            // Load next level
            Debug.Log("Next level not implemented yet");
        }

        private void OnRetryClicked()
        {
            GameManager.Instance.RestartLevel();
        }

        private void OnBackToMenuClicked()
        {
            GameManager.Instance.ReturnToMainMenu();
        }

        private void OnTowerButtonClicked(int towerIndex)
        {
            if (towerIndex >= 0 && towerIndex < System.Enum.GetValues(typeof(TowerType)).Length)
            {
                TowerType towerType = (TowerType)towerIndex;
                ToggleTowerBuildMode(towerType);
            }
        }

        private void OnBuildTowerClicked(int towerIndex)
        {
            if (towerIndex >= 0 && towerIndex < System.Enum.GetValues(typeof(TowerType)).Length)
            {
                TowerType towerType = (TowerType)towerIndex;
                StartTowerBuildMode(towerType);
            }
        }

        private void ToggleTowerBuildMode(TowerType towerType)
        {
            isTowerBuildMode = !isTowerBuildMode;
            
            if (towerSystem != null)
            {
                towerSystem.SetBuildMode(isTowerBuildMode, towerType);
            }

            if (towerBuildPanel != null)
            {
                towerBuildPanel.SetActive(isTowerBuildMode);
            }

            UpdateTowerInfo(towerType);
        }

        private void StartTowerBuildMode(TowerType towerType)
        {
            isTowerBuildMode = true;
            
            if (towerSystem != null)
            {
                towerSystem.SetBuildMode(true, towerType);
            }

            UpdateTowerInfo(towerType);
        }

        private void UpdateTowerInfo(TowerType towerType)
        {
            if (towerInfoText != null)
            {
                towerInfoText.text = towerSystem.GetTowerDescription(towerType);
            }

            if (towerCostText != null)
            {
                var cost = towerSystem.GetTowerCost(towerType);
                string costString = "建造成本:\n";
                foreach (var kvp in cost)
                {
                    costString += $"{kvp.Key}: {kvp.Value}\n";
                }
                towerCostText.text = costString;
            }
        }

        private LevelData CreateTestLevel()
        {
            var level = new LevelData
            {
                levelId = 1,
                levelName = "测试关卡",
                chapterName = "废墟觉醒",
                difficulty = 1,
                gridSize = new Vector2Int(8, 8),
                initialPollution = 100,
                targetPollution = 10,
                stepLimit = 50,
                timeLimit = 0f,
                purifierPosition = new Vector2Int(7, 0)
            };

            // Add some waste blocks
            for (int i = 0; i < 20; i++)
            {
                var wasteBlock = new WasteBlockData
                {
                    type = (WasteBlockType)Random.Range(0, 6),
                    position = new Vector2Int(Random.Range(0, 8), Random.Range(0, 8)),
                    pollutionValue = 2
                };
                level.wasteBlocks.Add(wasteBlock);
            }

            // Add basic rewards
            level.basicReward[ResourceType.Metal] = 20;
            level.basicReward[ResourceType.Plastic] = 15;
            level.basicReward[ResourceType.Core] = 2;

            return level;
        }

        private void OnDestroy()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.OnGameStateChanged -= OnGameStateChanged;
                GameManager.Instance.OnStepsChanged -= OnStepsChanged;
                GameManager.Instance.OnTimeChanged -= OnTimeChanged;
                GameManager.Instance.OnPollutionChanged -= OnPollutionChanged;
                GameManager.Instance.OnResourcesChanged -= OnResourcesChanged;
            }
        }
    }
}
