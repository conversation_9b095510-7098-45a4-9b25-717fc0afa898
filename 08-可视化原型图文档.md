# 《废土再生：回收者大作战》可视化原型图文档

## 1. 游戏流程图

### 1.1 主要游戏流程

```mermaid
flowchart TD
    A[启动游戏] --> B[主菜单]
    B --> C[开始游戏]
    B --> D[基地建设]
    B --> E[商店]
    B --> F[设置]
    
    C --> G[关卡选择]
    G --> H[关卡游戏]
    H --> I{关卡结果}
    I -->|成功| J[奖励结算]
    I -->|失败| K[重试选择]
    K -->|重试| H
    K -->|放弃| G
    J --> L[经验升级]
    L --> G
    
    D --> M[建筑管理]
    M --> N[资源分配]
    N --> O[科技研发]
    O --> D
    
    E --> P[道具购买]
    E --> Q[皮肤商店]
    E --> R[订阅服务]
```

### 1.2 新手引导流程

```mermaid
flowchart TD
    A[首次启动] --> B[观看开场动画]
    B --> C[创建角色]
    C --> D[基础操作教学]
    D --> E[第一次消除]
    E --> F[连锁反应演示]
    F --> G[建造防御设施]
    G --> H[抵御第一波敌人]
    H --> I[基地介绍]
    I --> J[完成第一关]
    J --> K[获得奖励]
    K --> L[解锁功能提示]
    L --> M[引导完成]
```

## 2. 系统架构图

### 2.1 客户端架构

```mermaid
graph TB
    subgraph "表现层"
        A[UI系统]
        B[渲染系统]
        C[音频系统]
        D[输入系统]
    end
    
    subgraph "逻辑层"
        E[游戏管理器]
        F[关卡系统]
        G[消除系统]
        H[塔防系统]
        I[建造系统]
    end
    
    subgraph "数据层"
        J[存档系统]
        K[配置系统]
        L[网络系统]
        M[缓存系统]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    E --> G
    E --> H
    E --> I
    
    F --> J
    G --> K
    H --> L
    I --> M
```

### 2.2 服务端架构

```mermaid
graph TB
    subgraph "客户端"
        A[Unity游戏客户端]
    end
    
    subgraph "网关层"
        B[API网关]
        C[负载均衡器]
    end
    
    subgraph "服务层"
        D[用户服务]
        E[游戏服务]
        F[社交服务]
        G[支付服务]
        H[数据分析服务]
    end
    
    subgraph "数据层"
        I[MongoDB]
        J[Redis]
        K[MySQL]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    C --> H
    
    D --> I
    E --> I
    F --> J
    G --> K
    H --> K
```

## 3. 界面原型图

### 3.1 主菜单界面结构

```mermaid
graph TD
    A[主菜单] --> B[顶部状态栏]
    A --> C[中央按钮区]
    A --> D[底部信息栏]
    
    B --> E[设置按钮]
    B --> F[商店按钮]
    B --> G[邮件按钮]
    
    C --> H[开始游戏]
    C --> I[基地建设]
    C --> J[图鉴收集]
    C --> K[每日挑战]
    
    D --> L[玩家头像]
    D --> M[等级显示]
    D --> N[金币数量]
    D --> O[钻石数量]
```

### 3.2 游戏界面布局

```mermaid
graph TD
    A[游戏界面] --> B[顶部状态区]
    A --> C[中央游戏区]
    A --> D[底部功能区]
    
    B --> E[污染值显示]
    B --> F[步数显示]
    B --> G[资源显示]
    B --> H[暂停按钮]
    
    C --> I[8x8游戏网格]
    C --> J[敌人路径]
    C --> K[防御设施]
    C --> L[净化装置]
    
    D --> M[建造菜单]
    D --> N[道具栏]
    D --> O[技能按钮]
```

## 4. 数据流图

### 4.1 关卡游戏数据流

```mermaid
sequenceDiagram
    participant P as 玩家
    participant C as 客户端
    participant S as 服务端
    participant D as 数据库
    
    P->>C: 选择关卡
    C->>S: 请求关卡数据
    S->>D: 查询关卡配置
    D->>S: 返回关卡数据
    S->>C: 发送关卡数据
    C->>P: 显示关卡界面
    
    P->>C: 执行消除操作
    C->>C: 本地验证操作
    C->>P: 显示消除效果
    
    P->>C: 完成关卡
    C->>S: 提交关卡结果
    S->>S: 验证结果合法性
    S->>D: 更新玩家进度
    D->>S: 确认更新成功
    S->>C: 返回奖励数据
    C->>P: 显示奖励界面
```

### 4.2 基地建设数据流

```mermaid
sequenceDiagram
    participant P as 玩家
    participant C as 客户端
    participant S as 服务端
    participant D as 数据库
    
    P->>C: 进入基地界面
    C->>S: 请求基地数据
    S->>D: 查询基地状态
    D->>S: 返回基地数据
    S->>C: 发送基地数据
    C->>P: 显示基地界面
    
    P->>C: 选择建造建筑
    C->>C: 检查资源是否足够
    C->>S: 发送建造请求
    S->>S: 验证建造条件
    S->>D: 扣除资源，开始建造
    D->>S: 确认操作成功
    S->>C: 返回建造状态
    C->>P: 显示建造进度
    
    Note over C,S: 建造完成后
    S->>C: 推送建造完成通知
    C->>P: 显示完成动画
```

## 5. 用户体验流程图

### 5.1 新用户转化漏斗

```mermaid
funnel
    title 新用户转化漏斗
    "应用商店访问" : 100000
    "下载安装" : 25000
    "首次启动" : 20000
    "完成引导" : 16000
    "完成第一关" : 12000
    "第二日留存" : 8000
    "第七日留存" : 3000
    "首次付费" : 600
```

### 5.2 付费用户行为路径

```mermaid
journey
    title 付费用户行为路径
    section 发现需求
      遇到困难关卡: 3: 用户
      查看商店: 4: 用户
      比较商品: 3: 用户
    section 购买决策
      选择商品: 4: 用户
      确认购买: 5: 用户
      完成支付: 4: 用户
    section 使用体验
      使用道具: 5: 用户
      通过关卡: 5: 用户
      获得成就感: 5: 用户
    section 后续行为
      继续游戏: 5: 用户
      推荐朋友: 4: 用户
      重复购买: 4: 用户
```

## 6. 技术架构图

### 6.1 网络通信架构

```mermaid
graph LR
    subgraph "客户端"
        A[Unity客户端]
        B[网络管理器]
        C[数据缓存]
    end
    
    subgraph "CDN"
        D[静态资源]
        E[更新包]
    end
    
    subgraph "服务端"
        F[API网关]
        G[微服务集群]
        H[数据库集群]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    F --> G
    G --> H
```

### 6.2 数据存储架构

```mermaid
erDiagram
    USER ||--o{ GAME_SESSION : has
    USER ||--o{ PLAYER_PROGRESS : has
    USER ||--o{ PURCHASE_RECORD : has
    
    GAME_SESSION ||--o{ MOVE_RECORD : contains
    PLAYER_PROGRESS ||--o{ LEVEL_COMPLETION : includes
    PLAYER_PROGRESS ||--o{ BUILDING_DATA : includes
    
    USER {
        string user_id PK
        string username
        string email
        datetime created_at
        datetime last_login
    }
    
    GAME_SESSION {
        string session_id PK
        string user_id FK
        int level_id
        datetime start_time
        datetime end_time
        string status
    }
    
    PLAYER_PROGRESS {
        string user_id PK
        int current_level
        int total_experience
        json resources
        json buildings
    }
```

## 7. 运营数据看板

### 7.1 关键指标监控

```mermaid
graph TB
    subgraph "用户指标"
        A[DAU: 50,000]
        B[MAU: 200,000]
        C[留存率: 15%]
    end
    
    subgraph "收入指标"
        D[月收入: ¥100万]
        E[ARPU: ¥10]
        F[付费率: 2%]
    end
    
    subgraph "游戏指标"
        G[平均时长: 45分钟]
        H[关卡通过率: 65%]
        I[重试率: 25%]
    end
    
    subgraph "技术指标"
        J[崩溃率: 0.05%]
        K[加载时间: 2.5秒]
        L[响应时间: 80ms]
    end
```

### 7.2 用户行为分析

```mermaid
pie title 用户游戏时长分布
    "0-15分钟" : 25
    "15-30分钟" : 35
    "30-60分钟" : 25
    "60分钟以上" : 15
```

```mermaid
pie title 用户付费分布
    "免费用户" : 98
    "小额付费(1-50元)" : 1.5
    "中额付费(50-200元)" : 0.4
    "大额付费(200元以上)" : 0.1
```

## 8. 开发工作流程图

### 8.1 功能开发流程

```mermaid
gitgraph
    commit id: "项目初始化"
    branch feature/match-system
    checkout feature/match-system
    commit id: "消除系统开发"
    commit id: "特效添加"
    checkout main
    merge feature/match-system
    
    branch feature/tower-defense
    checkout feature/tower-defense
    commit id: "塔防系统开发"
    commit id: "AI优化"
    checkout main
    merge feature/tower-defense
    
    branch feature/base-building
    checkout feature/base-building
    commit id: "建造系统开发"
    commit id: "UI优化"
    checkout main
    merge feature/base-building
    
    commit id: "版本发布"
```

### 8.2 测试发布流程

```mermaid
flowchart LR
    A[开发完成] --> B[代码审查]
    B --> C[单元测试]
    C --> D[集成测试]
    D --> E{测试通过?}
    E -->|否| F[修复问题]
    F --> C
    E -->|是| G[构建版本]
    G --> H[内部测试]
    H --> I{内测通过?}
    I -->|否| J[问题修复]
    J --> G
    I -->|是| K[外部测试]
    K --> L[收集反馈]
    L --> M[优化调整]
    M --> N[正式发布]
```

---

*可视化原型图帮助团队更好地理解游戏的整体架构和用户体验流程，通过图表的形式清晰地展示了系统间的关系和数据流向，为开发和运营提供了直观的指导。*
