using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 游戏测试器 - 用于测试和验证游戏系统
    /// 在开发阶段可以用来快速测试各种功能
    /// </summary>
    public class GameTester : MonoBehaviour
    {
        [Header("Test Settings")]
        public bool enableTesting = true;
        public bool autoStartTest = false;
        public float testDelay = 2f;

        [Header("Test Scenarios")]
        public bool testBasicGameplay = true;
        public bool testTowerBuilding = true;
        public bool testEnemySpawning = true;
        public bool testSaveSystem = true;
        public bool testAudioSystem = true;

        [Header("Debug Options")]
        public bool showDebugInfo = true;
        public bool logDetailedInfo = false;

        private void Start()
        {
            if (enableTesting && autoStartTest)
            {
                StartCoroutine(RunAllTests());
            }
        }

        private void Update()
        {
            // 快捷键测试
            if (enableTesting)
            {
                HandleTestInputs();
            }

            if (showDebugInfo)
            {
                DisplayDebugInfo();
            }
        }

        private void HandleTestInputs()
        {
            // T键 - 运行所有测试
            if (Input.GetKeyDown(KeyCode.T))
            {
                StartCoroutine(RunAllTests());
            }

            // 1键 - 测试基础玩法
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                StartCoroutine(TestBasicGameplay());
            }

            // 2键 - 测试塔防系统
            if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                StartCoroutine(TestTowerBuilding());
            }

            // 3键 - 测试敌人系统
            if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                StartCoroutine(TestEnemySpawning());
            }

            // 4键 - 测试存档系统
            if (Input.GetKeyDown(KeyCode.Alpha4))
            {
                TestSaveSystem();
            }

            // 5键 - 测试音频系统
            if (Input.GetKeyDown(KeyCode.Alpha5))
            {
                TestAudioSystem();
            }

            // R键 - 重启当前关卡
            if (Input.GetKeyDown(KeyCode.R))
            {
                if (GameManager.Instance != null)
                {
                    GameManager.Instance.RestartLevel();
                }
            }

            // ESC键 - 返回主菜单
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (GameManager.Instance != null)
                {
                    GameManager.Instance.ReturnToMainMenu();
                }
            }
        }

        private IEnumerator RunAllTests()
        {
            Debug.Log("=== 开始运行所有测试 ===");

            if (testBasicGameplay)
            {
                yield return StartCoroutine(TestBasicGameplay());
                yield return new WaitForSeconds(testDelay);
            }

            if (testTowerBuilding)
            {
                yield return StartCoroutine(TestTowerBuilding());
                yield return new WaitForSeconds(testDelay);
            }

            if (testEnemySpawning)
            {
                yield return StartCoroutine(TestEnemySpawning());
                yield return new WaitForSeconds(testDelay);
            }

            if (testSaveSystem)
            {
                TestSaveSystem();
                yield return new WaitForSeconds(testDelay);
            }

            if (testAudioSystem)
            {
                TestAudioSystem();
                yield return new WaitForSeconds(testDelay);
            }

            Debug.Log("=== 所有测试完成 ===");
        }

        private IEnumerator TestBasicGameplay()
        {
            Debug.Log("--- 测试基础玩法 ---");

            // 确保游戏管理器存在
            if (GameManager.Instance == null)
            {
                Debug.LogError("GameManager not found!");
                yield break;
            }

            // 创建测试关卡
            LevelData testLevel = CreateTestLevel();
            GameManager.Instance.StartLevel(testLevel);

            yield return new WaitForSeconds(1f);

            // 测试网格系统
            GridSystem gridSystem = FindObjectOfType<GridSystem>();
            if (gridSystem != null)
            {
                Debug.Log("✓ GridSystem found and initialized");
                
                // 测试消除
                Vector2Int testPosition = new Vector2Int(2, 2);
                bool matchResult = gridSystem.TryMatch(testPosition);
                Debug.Log($"Match test at {testPosition}: {matchResult}");
            }
            else
            {
                Debug.LogError("✗ GridSystem not found!");
            }

            // 测试消除系统
            MatchSystem matchSystem = FindObjectOfType<MatchSystem>();
            if (matchSystem != null)
            {
                Debug.Log("✓ MatchSystem found and initialized");
            }
            else
            {
                Debug.LogError("✗ MatchSystem not found!");
            }

            Debug.Log("--- 基础玩法测试完成 ---");
        }

        private IEnumerator TestTowerBuilding()
        {
            Debug.Log("--- 测试塔防系统 ---");

            TowerSystem towerSystem = FindObjectOfType<TowerSystem>();
            if (towerSystem == null)
            {
                Debug.LogError("✗ TowerSystem not found!");
                yield break;
            }

            Debug.Log("✓ TowerSystem found");

            // 测试建造防御塔
            Vector2Int buildPosition = new Vector2Int(1, 1);
            TowerType testTowerType = TowerType.WindFilter;

            bool buildResult = towerSystem.TryBuildTower(buildPosition, testTowerType);
            Debug.Log($"Tower build test at {buildPosition}: {buildResult}");

            yield return new WaitForSeconds(1f);

            // 测试升级防御塔
            bool upgradeResult = towerSystem.TryUpgradeTower(buildPosition);
            Debug.Log($"Tower upgrade test: {upgradeResult}");

            Debug.Log("--- 塔防系统测试完成 ---");
        }

        private IEnumerator TestEnemySpawning()
        {
            Debug.Log("--- 测试敌人系统 ---");

            EnemySystem enemySystem = FindObjectOfType<EnemySystem>();
            if (enemySystem == null)
            {
                Debug.LogError("✗ EnemySystem not found!");
                yield break;
            }

            Debug.Log("✓ EnemySystem found");

            // 测试生成敌人
            Vector2Int spawnPos = new Vector2Int(0, 4);
            List<Vector2Int> testPath = new List<Vector2Int>
            {
                new Vector2Int(1, 4),
                new Vector2Int(2, 4),
                new Vector2Int(3, 4),
                new Vector2Int(4, 4)
            };

            enemySystem.SpawnEnemy(EnemyType.PollutionCrawler, spawnPos, testPath);
            Debug.Log("Enemy spawn test completed");

            yield return new WaitForSeconds(2f);

            // 检查敌人数量
            int enemyCount = enemySystem.GetActiveEnemyCount();
            Debug.Log($"Active enemies: {enemyCount}");

            Debug.Log("--- 敌人系统测试完成 ---");
        }

        private void TestSaveSystem()
        {
            Debug.Log("--- 测试存档系统 ---");

            // 测试保存
            PlayerData testData = new PlayerData();
            testData.playerName = "TestPlayer";
            testData.level = 5;
            testData.experience = 1000;
            testData.resources[ResourceType.Metal] = 100;

            SaveSystem.SavePlayerData(testData);
            Debug.Log("✓ Save test completed");

            // 测试加载
            PlayerData loadedData = SaveSystem.LoadPlayerData();
            if (loadedData != null)
            {
                Debug.Log($"✓ Load test completed - Player: {loadedData.playerName}, Level: {loadedData.level}");
            }
            else
            {
                Debug.LogError("✗ Load test failed");
            }

            Debug.Log("--- 存档系统测试完成 ---");
        }

        private void TestAudioSystem()
        {
            Debug.Log("--- 测试音频系统 ---");

            AudioManager audioManager = FindObjectOfType<AudioManager>();
            if (audioManager == null)
            {
                Debug.LogError("✗ AudioManager not found!");
                return;
            }

            Debug.Log("✓ AudioManager found");

            // 测试音效播放
            audioManager.PlaySFX("match");
            Debug.Log("SFX test completed");

            // 测试音乐播放
            audioManager.PlayMusic("default_theme");
            Debug.Log("Music test completed");

            Debug.Log("--- 音频系统测试完成 ---");
        }

        private LevelData CreateTestLevel()
        {
            var level = new LevelData
            {
                levelId = 999,
                levelName = "测试关卡",
                chapterName = "测试章节",
                difficulty = 1,
                gridSize = new Vector2Int(8, 8),
                initialPollution = 100,
                targetPollution = 10,
                stepLimit = 50,
                timeLimit = 0f,
                purifierPosition = new Vector2Int(7, 0)
            };

            // 添加测试废料块
            for (int i = 0; i < 15; i++)
            {
                var wasteBlock = new WasteBlockData
                {
                    type = (WasteBlockType)(i % 6),
                    position = new Vector2Int(Random.Range(0, 6), Random.Range(0, 8)),
                    pollutionValue = 2
                };
                level.wasteBlocks.Add(wasteBlock);
            }

            // 添加测试敌人波次
            var enemyWave = new EnemyWaveData
            {
                spawnTime = 5f,
                enemyType = EnemyType.PollutionCrawler,
                count = 3,
                spawnInterval = 1f
            };
            enemyWave.spawnPositions.Add(new Vector2Int(0, 4));
            enemyWave.path.Add(new Vector2Int(1, 4));
            enemyWave.path.Add(new Vector2Int(2, 4));
            enemyWave.path.Add(new Vector2Int(7, 0));
            level.enemyWaves.Add(enemyWave);

            // 添加基础奖励
            level.basicReward[ResourceType.Metal] = 20;
            level.basicReward[ResourceType.Plastic] = 15;
            level.basicReward[ResourceType.Core] = 2;

            return level;
        }

        private void DisplayDebugInfo()
        {
            if (GameManager.Instance == null) return;

            // 在屏幕上显示调试信息
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            GUILayout.BeginVertical("box");

            GUILayout.Label("=== 游戏调试信息 ===");
            GUILayout.Label($"游戏状态: {GameManager.Instance.currentState}");
            
            if (GameManager.Instance.currentLevel != null)
            {
                GUILayout.Label($"当前关卡: {GameManager.Instance.currentLevel.levelName}");
            }

            GUILayout.Space(10);
            GUILayout.Label("=== 快捷键 ===");
            GUILayout.Label("T - 运行所有测试");
            GUILayout.Label("1 - 测试基础玩法");
            GUILayout.Label("2 - 测试塔防系统");
            GUILayout.Label("3 - 测试敌人系统");
            GUILayout.Label("4 - 测试存档系统");
            GUILayout.Label("5 - 测试音频系统");
            GUILayout.Label("R - 重启关卡");
            GUILayout.Label("ESC - 返回主菜单");

            GUILayout.Space(10);
            if (GUILayout.Button("运行所有测试"))
            {
                StartCoroutine(RunAllTests());
            }

            if (GUILayout.Button("创建测试关卡"))
            {
                LevelData testLevel = CreateTestLevel();
                GameManager.Instance.StartLevel(testLevel);
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        private void OnGUI()
        {
            if (showDebugInfo && enableTesting)
            {
                DisplayDebugInfo();
            }
        }

        // 性能测试
        public void TestPerformance()
        {
            Debug.Log("--- 性能测试开始 ---");

            float startTime = Time.realtimeSinceStartup;

            // 测试大量对象创建
            for (int i = 0; i < 1000; i++)
            {
                GameObject testObj = new GameObject("TestObject");
                Destroy(testObj);
            }

            float endTime = Time.realtimeSinceStartup;
            Debug.Log($"对象创建销毁测试: {(endTime - startTime) * 1000f:F2}ms");

            // 测试事件系统性能
            startTime = Time.realtimeSinceStartup;
            for (int i = 0; i < 10000; i++)
            {
                EventSystem.Publish(new MatchEvent());
            }
            endTime = Time.realtimeSinceStartup;
            Debug.Log($"事件系统测试: {(endTime - startTime) * 1000f:F2}ms");

            Debug.Log("--- 性能测试完成 ---");
        }

        // 内存测试
        public void TestMemoryUsage()
        {
            Debug.Log("--- 内存使用测试 ---");

            long beforeMemory = System.GC.GetTotalMemory(false);
            Debug.Log($"测试前内存: {beforeMemory / 1024 / 1024}MB");

            // 创建大量对象测试内存
            List<GameObject> testObjects = new List<GameObject>();
            for (int i = 0; i < 1000; i++)
            {
                GameObject obj = new GameObject($"MemoryTest_{i}");
                testObjects.Add(obj);
            }

            long afterMemory = System.GC.GetTotalMemory(false);
            Debug.Log($"创建对象后内存: {afterMemory / 1024 / 1024}MB");

            // 清理对象
            foreach (var obj in testObjects)
            {
                Destroy(obj);
            }
            testObjects.Clear();

            System.GC.Collect();
            long cleanupMemory = System.GC.GetTotalMemory(true);
            Debug.Log($"清理后内存: {cleanupMemory / 1024 / 1024}MB");

            Debug.Log("--- 内存使用测试完成 ---");
        }
    }
}
