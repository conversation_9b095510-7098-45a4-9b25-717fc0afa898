using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 增强的消除系统 - 丰富的玩法机制
    /// </summary>
    public class EnhancedMatchSystem : MonoBehaviour
    {
        [Header("消除规则")]
        public int minMatchCount = 3;
        public bool allowDiagonalMatch = false;
        public bool enableSpecialBlocks = true;
        public bool enableChainReactions = true;

        [Header("特殊块类型")]
        public float bombBlockChance = 0.1f;
        public float lineBlockChance = 0.08f;
        public float colorBombChance = 0.05f;

        [Header("连击系统")]
        public float comboTimeWindow = 2f;
        public float[] comboMultipliers = { 1f, 1.2f, 1.5f, 2f, 3f, 5f };

        [Header("视觉效果")]
        public GameObject explosionEffect;
        public GameObject lineEffect;
        public GameObject colorBombEffect;
        public AudioClip matchSound;
        public AudioClip comboSound;
        public AudioClip specialSound;

        private GridSystem gridSystem;
        private int currentCombo = 0;
        private float lastMatchTime = 0f;
        private Queue<Vector2Int> pendingMatches = new Queue<Vector2Int>();
        private bool isProcessingMatches = false;

        public void Initialize()
        {
            gridSystem = FindObjectOfType<GridSystem>();
            if (gridSystem != null)
            {
                gridSystem.OnCellClickedEvent += OnCellClicked;
            }
        }

        private void OnCellClicked(Vector2Int position)
        {
            if (isProcessingMatches) return;

            WasteBlock block = gridSystem.GetWasteBlock(position);
            if (block == null) return;

            StartCoroutine(ProcessMatch(position, block));
        }

        private IEnumerator ProcessMatch(Vector2Int position, WasteBlock block)
        {
            isProcessingMatches = true;

            // 检查特殊块
            if (block.blockType == WasteBlockType.Bomb)
            {
                yield return StartCoroutine(ProcessBombBlock(position));
            }
            else if (block.blockType == WasteBlockType.LineHorizontal || block.blockType == WasteBlockType.LineVertical)
            {
                yield return StartCoroutine(ProcessLineBlock(position, block.blockType));
            }
            else if (block.blockType == WasteBlockType.ColorBomb)
            {
                yield return StartCoroutine(ProcessColorBomb(position, block.wasteType));
            }
            else
            {
                // 普通消除
                List<Vector2Int> matches = FindMatches(position, block.wasteType);
                if (matches.Count >= minMatchCount)
                {
                    yield return StartCoroutine(ProcessNormalMatch(matches, block.wasteType));
                }
            }

            // 处理重力和新块生成
            yield return StartCoroutine(ApplyGravityAndRefill());

            // 检查连锁反应
            if (enableChainReactions)
            {
                yield return StartCoroutine(CheckChainReactions());
            }

            isProcessingMatches = false;
        }

        private List<Vector2Int> FindMatches(Vector2Int startPos, WasteBlockType targetType)
        {
            List<Vector2Int> matches = new List<Vector2Int>();
            HashSet<Vector2Int> visited = new HashSet<Vector2Int>();
            Queue<Vector2Int> toCheck = new Queue<Vector2Int>();

            toCheck.Enqueue(startPos);
            visited.Add(startPos);

            while (toCheck.Count > 0)
            {
                Vector2Int current = toCheck.Dequeue();
                WasteBlock currentBlock = gridSystem.GetWasteBlock(current);

                if (currentBlock != null && currentBlock.blockType == targetType)
                {
                    matches.Add(current);

                    // 检查相邻位置
                    Vector2Int[] directions = {
                        Vector2Int.up, Vector2Int.down, Vector2Int.left, Vector2Int.right
                    };

                    if (allowDiagonalMatch)
                    {
                        Vector2Int[] diagonals = {
                            new Vector2Int(1, 1), new Vector2Int(1, -1),
                            new Vector2Int(-1, 1), new Vector2Int(-1, -1)
                        };
                        System.Array.Resize(ref directions, directions.Length + diagonals.Length);
                        System.Array.Copy(diagonals, 0, directions, 4, diagonals.Length);
                    }

                    foreach (Vector2Int dir in directions)
                    {
                        Vector2Int neighbor = current + dir;
                        if (!visited.Contains(neighbor) && gridSystem.IsValidPosition(neighbor))
                        {
                            visited.Add(neighbor);
                            toCheck.Enqueue(neighbor);
                        }
                    }
                }
            }

            return matches;
        }

        private IEnumerator ProcessNormalMatch(List<Vector2Int> matches, WasteBlockType matchType)
        {
            // 更新连击
            UpdateCombo();

            // 计算分数
            int baseScore = matches.Count * 10;
            float multiplier = GetComboMultiplier();
            int finalScore = Mathf.RoundToInt(baseScore * multiplier);

            // 播放音效
            PlayMatchSound(matches.Count);

            // 创建特殊块（如果符合条件）
            Vector2Int specialBlockPos = Vector2Int.zero;
            WasteBlockType specialType = WasteBlockType.Metal;
            bool createSpecial = false;

            if (matches.Count >= 5 && enableSpecialBlocks)
            {
                createSpecial = true;
                specialBlockPos = matches[0];
                specialType = DetermineSpecialBlockType(matches);
            }

            // 消除动画
            yield return StartCoroutine(AnimateBlockDestruction(matches));

            // 生成资源
            Dictionary<ResourceType, int> resources = CalculateResources(matches, matchType, multiplier);

            // 发布事件
            EventSystem.Publish(new MatchEvent
            {
                matchedBlocks = matches,
                matchType = matchType,
                score = finalScore,
                resources = resources,
                position = gridSystem.GridToWorldPosition(matches[0])
            });

            EventSystem.Publish(new ScoreEvent
            {
                points = finalScore,
                position = gridSystem.GridToWorldPosition(matches[0])
            });

            EventSystem.Publish(new ResourceGainEvent { resources = resources });

            // 创建特殊块
            if (createSpecial)
            {
                yield return new WaitForSeconds(0.3f);
                CreateSpecialBlock(specialBlockPos, specialType);
            }
        }

        private IEnumerator ProcessBombBlock(Vector2Int position)
        {
            List<Vector2Int> affectedPositions = new List<Vector2Int>();

            // 3x3范围爆炸
            for (int x = -1; x <= 1; x++)
            {
                for (int y = -1; y <= 1; y++)
                {
                    Vector2Int targetPos = position + new Vector2Int(x, y);
                    if (gridSystem.IsValidPosition(targetPos))
                    {
                        affectedPositions.Add(targetPos);
                    }
                }
            }

            // 播放爆炸特效
            if (explosionEffect != null)
            {
                Vector3 worldPos = gridSystem.GridToWorldPosition(position);
                Instantiate(explosionEffect, worldPos, Quaternion.identity);
            }

            PlaySpecialSound();

            yield return StartCoroutine(AnimateBlockDestruction(affectedPositions));

            // 计算奖励
            int score = affectedPositions.Count * 25;
            EventSystem.Publish(new ScoreEvent
            {
                points = score,
                position = gridSystem.GridToWorldPosition(position)
            });
        }

        private IEnumerator ProcessLineBlock(Vector2Int position, WasteBlockType lineType)
        {
            List<Vector2Int> affectedPositions = new List<Vector2Int>();

            if (lineType == WasteBlockType.LineHorizontal)
            {
                // 消除整行
                for (int x = 0; x < gridSystem.gridWidth; x++)
                {
                    Vector2Int targetPos = new Vector2Int(x, position.y);
                    if (gridSystem.IsValidPosition(targetPos))
                    {
                        affectedPositions.Add(targetPos);
                    }
                }
            }
            else if (lineType == WasteBlockType.LineVertical)
            {
                // 消除整列
                for (int y = 0; y < gridSystem.gridHeight; y++)
                {
                    Vector2Int targetPos = new Vector2Int(position.x, y);
                    if (gridSystem.IsValidPosition(targetPos))
                    {
                        affectedPositions.Add(targetPos);
                    }
                }
            }

            // 播放线条特效
            if (lineEffect != null)
            {
                Vector3 worldPos = gridSystem.GridToWorldPosition(position);
                GameObject effect = Instantiate(lineEffect, worldPos, Quaternion.identity);
                
                if (lineType == WasteBlockType.LineVertical)
                {
                    effect.transform.Rotate(0, 0, 90);
                }
            }

            PlaySpecialSound();

            yield return StartCoroutine(AnimateBlockDestruction(affectedPositions));

            int score = affectedPositions.Count * 20;
            EventSystem.Publish(new ScoreEvent
            {
                points = score,
                position = gridSystem.GridToWorldPosition(position)
            });
        }

        private IEnumerator ProcessColorBomb(Vector2Int position, WasteBlockType targetColor)
        {
            List<Vector2Int> affectedPositions = new List<Vector2Int>();

            // 找到所有相同颜色的块
            for (int x = 0; x < gridSystem.gridWidth; x++)
            {
                for (int y = 0; y < gridSystem.gridHeight; y++)
                {
                    Vector2Int checkPos = new Vector2Int(x, y);
                    WasteBlock block = gridSystem.GetWasteBlock(checkPos);
                    if (block != null && block.blockType == targetColor)
                    {
                        affectedPositions.Add(checkPos);
                    }
                }
            }

            // 播放颜色炸弹特效
            if (colorBombEffect != null)
            {
                Vector3 worldPos = gridSystem.GridToWorldPosition(position);
                Instantiate(colorBombEffect, worldPos, Quaternion.identity);
            }

            PlaySpecialSound();

            yield return StartCoroutine(AnimateBlockDestruction(affectedPositions));

            int score = affectedPositions.Count * 30;
            EventSystem.Publish(new ScoreEvent
            {
                points = score,
                position = gridSystem.GridToWorldPosition(position)
            });
        }

        private IEnumerator AnimateBlockDestruction(List<Vector2Int> positions)
        {
            List<Coroutine> animations = new List<Coroutine>();

            foreach (Vector2Int pos in positions)
            {
                WasteBlock block = gridSystem.GetWasteBlock(pos);
                if (block != null)
                {
                    animations.Add(StartCoroutine(AnimateSingleBlockDestruction(block)));
                }
            }

            // 等待所有动画完成
            foreach (Coroutine anim in animations)
            {
                yield return anim;
            }

            // 移除块
            foreach (Vector2Int pos in positions)
            {
                WasteBlock block = gridSystem.GetWasteBlock(pos);
                if (block != null)
                {
                    gridSystem.SetWasteBlock(pos, null);
                    Destroy(block.gameObject);
                }
            }
        }

        private IEnumerator AnimateSingleBlockDestruction(WasteBlock block)
        {
            Vector3 originalScale = block.transform.localScale;
            Vector3 originalPosition = block.transform.position;

            float duration = 0.4f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;

                // 缩放和旋转动画
                float scale = Mathf.Lerp(1f, 0f, t);
                float rotation = Mathf.Lerp(0f, 360f, t);
                
                block.transform.localScale = originalScale * scale;
                block.transform.rotation = Quaternion.Euler(0, 0, rotation);
                
                // 轻微的位移效果
                Vector3 offset = new Vector3(
                    Mathf.Sin(t * Mathf.PI * 4) * 0.1f,
                    Mathf.Sin(t * Mathf.PI * 2) * 0.2f,
                    0
                );
                block.transform.position = originalPosition + offset;

                yield return null;
            }
        }

        private IEnumerator ApplyGravityAndRefill()
        {
            // 应用重力
            bool blocksMoving = true;
            while (blocksMoving)
            {
                blocksMoving = false;
                
                for (int x = 0; x < gridSystem.gridWidth; x++)
                {
                    for (int y = 1; y < gridSystem.gridHeight; y++)
                    {
                        Vector2Int currentPos = new Vector2Int(x, y);
                        Vector2Int belowPos = new Vector2Int(x, y - 1);

                        if (gridSystem.GetWasteBlock(currentPos) != null &&
                            gridSystem.GetWasteBlock(belowPos) == null)
                        {
                            WasteBlock block = gridSystem.GetWasteBlock(currentPos);
                            gridSystem.SetWasteBlock(currentPos, null);
                            gridSystem.SetWasteBlock(belowPos, block);
                            block.transform.position = gridSystem.GridToWorldPosition(belowPos);
                            block.gridPosition = belowPos;
                            blocksMoving = true;
                        }
                    }
                }

                if (blocksMoving)
                {
                    yield return new WaitForSeconds(0.1f);
                }
            }

            // 填充新块
            yield return StartCoroutine(RefillGrid());
        }

        private IEnumerator RefillGrid()
        {
            for (int x = 0; x < gridSystem.gridWidth; x++)
            {
                int emptyCount = 0;
                
                // 计算空位数量
                for (int y = 0; y < gridSystem.gridHeight; y++)
                {
                    if (gridSystem.GetWasteBlock(new Vector2Int(x, y)) == null)
                    {
                        emptyCount++;
                    }
                }

                // 生成新块
                for (int i = 0; i < emptyCount; i++)
                {
                    Vector2Int newPos = new Vector2Int(x, gridSystem.gridHeight - 1 - i);
                    WasteBlockType newType = GetRandomWasteType();

                    WasteBlockData newBlockData = new WasteBlockData
                    {
                        type = newType,
                        position = newPos,
                        pollutionValue = 2
                    };
                    gridSystem.CreateWasteBlock(newBlockData);

                    yield return new WaitForSeconds(0.05f);
                }
            }
        }

        private IEnumerator CheckChainReactions()
        {
            yield return new WaitForSeconds(0.2f);

            bool foundChain = false;
            
            for (int x = 0; x < gridSystem.gridWidth; x++)
            {
                for (int y = 0; y < gridSystem.gridHeight; y++)
                {
                    Vector2Int pos = new Vector2Int(x, y);
                    WasteBlock block = gridSystem.GetWasteBlock(pos);
                    
                    if (block != null)
                    {
                        List<Vector2Int> matches = FindMatches(pos, block.blockType);
                        if (matches.Count >= minMatchCount)
                        {
                            foundChain = true;
                            yield return StartCoroutine(ProcessNormalMatch(matches, block.blockType));
                            break;
                        }
                    }
                }
                if (foundChain) break;
            }

            if (foundChain)
            {
                yield return StartCoroutine(ApplyGravityAndRefill());
                yield return StartCoroutine(CheckChainReactions());
            }
        }

        private void UpdateCombo()
        {
            float currentTime = Time.time;
            
            if (currentTime - lastMatchTime <= comboTimeWindow)
            {
                currentCombo++;
            }
            else
            {
                currentCombo = 1;
            }

            lastMatchTime = currentTime;

            if (currentCombo > 1)
            {
                EventSystem.Publish(new ComboEvent { comboCount = currentCombo });
            }
        }

        private float GetComboMultiplier()
        {
            int index = Mathf.Min(currentCombo - 1, comboMultipliers.Length - 1);
            return comboMultipliers[index];
        }

        private WasteBlockType DetermineSpecialBlockType(List<Vector2Int> matches)
        {
            if (matches.Count >= 7)
                return WasteBlockType.ColorBomb;
            else if (matches.Count >= 5)
                return Random.value > 0.5f ? WasteBlockType.LineHorizontal : WasteBlockType.LineVertical;
            else
                return WasteBlockType.Bomb;
        }

        private void CreateSpecialBlock(Vector2Int position, WasteBlockType specialType)
        {
            WasteBlockData specialBlockData = new WasteBlockData
            {
                type = specialType,
                position = position,
                pollutionValue = 2
            };
            gridSystem.CreateWasteBlock(specialBlockData);
        }

        private Dictionary<ResourceType, int> CalculateResources(List<Vector2Int> matches, WasteBlockType matchType, float multiplier)
        {
            Dictionary<ResourceType, int> resources = new Dictionary<ResourceType, int>();

            ResourceType resourceType = GetResourceTypeFromWaste(matchType);
            int baseAmount = matches.Count;
            int finalAmount = Mathf.RoundToInt(baseAmount * multiplier);

            resources[resourceType] = finalAmount;

            // 连击奖励
            if (currentCombo >= 3)
            {
                resources[ResourceType.Core] = 1;
            }

            return resources;
        }

        private ResourceType GetResourceTypeFromWaste(WasteBlockType wasteType)
        {
            switch (wasteType)
            {
                case WasteBlockType.Metal: return ResourceType.Metal;
                case WasteBlockType.Plastic: return ResourceType.Plastic;
                case WasteBlockType.Wood: return ResourceType.Wood;
                case WasteBlockType.Electronic: return ResourceType.Electronic;
                case WasteBlockType.Glass: return ResourceType.Glass;
                case WasteBlockType.Organic: return ResourceType.Organic;
                default: return ResourceType.Metal;
            }
        }

        private WasteBlockType GetRandomWasteType()
        {
            // 基础类型权重
            WasteBlockType[] basicTypes = {
                WasteBlockType.Metal, WasteBlockType.Plastic, WasteBlockType.Wood,
                WasteBlockType.Electronic, WasteBlockType.Glass, WasteBlockType.Organic
            };

            WasteBlockType selectedType = basicTypes[Random.Range(0, basicTypes.Length)];

            // 检查是否生成特殊块
            if (enableSpecialBlocks)
            {
                float rand = Random.value;
                if (rand < bombBlockChance)
                    return WasteBlockType.Bomb;
                else if (rand < bombBlockChance + lineBlockChance)
                    return Random.value > 0.5f ? WasteBlockType.LineHorizontal : WasteBlockType.LineVertical;
                else if (rand < bombBlockChance + lineBlockChance + colorBombChance)
                    return WasteBlockType.ColorBomb;
            }

            return selectedType;
        }

        private void PlayMatchSound(int matchCount)
        {
            if (matchSound != null)
            {
                AudioSource.PlayClipAtPoint(matchSound, Camera.main.transform.position);
            }

            if (currentCombo > 1 && comboSound != null)
            {
                AudioSource.PlayClipAtPoint(comboSound, Camera.main.transform.position);
            }
        }

        private void PlaySpecialSound()
        {
            if (specialSound != null)
            {
                AudioSource.PlayClipAtPoint(specialSound, Camera.main.transform.position);
            }
        }

        public void ResetCombo()
        {
            currentCombo = 0;
            lastMatchTime = 0f;
        }
    }
}
