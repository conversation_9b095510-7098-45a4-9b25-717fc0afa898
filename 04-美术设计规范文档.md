# 《废土再生：回收者大作战》美术设计规范文档

## 1. 美术风格定义

### 1.1 整体美术风格
**核心理念**: "废土重生" - 从绝望到希望的视觉转变
**风格关键词**: 后启示录、环保重生、科技感、温暖希望

**视觉对比设计**:
- **废土区域**: 暗沉、破败、污染感
- **净化区域**: 明亮、清新、生机勃勃
- **过渡区域**: 渐变效果，体现净化过程

### 1.2 色彩方案

#### 主色调定义
```
废土色系:
- 主色: 锈铁棕 (#8B4513)
- 辅色: 污染灰 (#696969) 
- 强调: 危险红 (#DC143C)

净化色系:
- 主色: 生命绿 (#32CD32)
- 辅色: 天空蓝 (#87CEEB)
- 强调: 希望金 (#FFD700)

科技色系:
- 主色: 科技蓝 (#1E90FF)
- 辅色: 能量紫 (#9370DB)
- 强调: 电光白 (#F0F8FF)
```

#### 色彩使用规范
**背景色彩**:
- 废土关卡: 以棕色、灰色为主，局部红色警示
- 工业关卡: 金属灰+锈色，机械蓝色点缀
- 核污染关卡: 绿色辐射光+黄色警告色

**UI色彩**:
- 主要按钮: 科技蓝渐变
- 次要按钮: 中性灰渐变
- 危险操作: 警告橙到危险红渐变
- 成功反馈: 生命绿渐变

### 1.3 材质风格

**废料块材质设计**:
- **金属废料**: 生锈表面+划痕+反光
- **塑料废料**: 半透明+色彩鲜艳+轻盈感
- **木材废料**: 腐朽纹理+虫蛀效果
- **电子废料**: 电路板纹理+LED闪烁
- **玻璃废料**: 透明+裂纹+光线折射
- **有机废料**: 腐烂质感+发酵气泡

**特殊效果材质**:
- **污染核心**: 黑色+红色脉冲+腐蚀边缘
- **再生核心**: 彩虹渐变+能量流动+光晕
- **变异废料**: 紫色+扭曲动画+不稳定效果

## 2. 角色与生物设计

### 2.1 变异生物设计

#### 污染爬虫
**设计概念**: 受污染影响的小型生物
**外观特征**:
- 体型: 类似蟑螂，长度15-20cm
- 颜色: 污浊的棕绿色，带有紫色斑点
- 材质: 湿润的甲壳，反射污染光泽
- 动画: 快速爬行，触角不断摆动

**设计草图描述**:
```
     触角
      /|\
   ╭─────╮  ← 头部 (污染绿色)
  ╱       ╲
 ╱  ●   ●  ╲ ← 复眼 (红色发光)
╱___________╲
│ ╱╲ ╱╲ ╱╲ │ ← 身体 (棕色甲壳)
│╱  ╲╱  ╲╱ │
└───────────┘
 ╱╲ ╱╲ ╱╲   ← 腿部 (6条腿)
```

#### 腐蚀飞虫
**设计概念**: 空中污染传播者
**外观特征**:
- 体型: 类似蜻蜓，翼展25-30cm
- 颜色: 透明翅膀+金属绿身体
- 特效: 翅膀振动模糊+酸液滴落
- 动画: 悬停+快速俯冲

#### 变异植物
**设计概念**: 被污染扭曲的植物
**外观特征**:
- 体型: 高度1-2米的藤蔓状
- 颜色: 病态的紫绿色+黑色斑点
- 特效: 缓慢蠕动+毒气释放
- 动画: 根部移动+触手摆动

#### 污染之王 (Boss)
**设计概念**: 污染的集合体，多形态Boss
**第一形态**: 巨大的污染团块
- 直径3-4米的黑色球体
- 表面不断冒泡+红色脉络
- 移动时留下污染痕迹

**第二形态**: 分裂成多个小体
- 3个中等大小的污染体
- 各自有不同的攻击模式
- 需要同时击败才能获胜

### 2.2 回收者角色设计

#### 主角设计
**基础外观**:
- 年龄: 25-30岁，中性化设计
- 服装: 功能性防护服+科技装备
- 配色: 蓝白色主调+橙色安全标识
- 装备: 净化器背包+多功能工具

**可定制元素**:
- 头盔样式: 5种不同科技风格
- 服装颜色: 8种配色方案
- 装备外观: 3种科技等级
- 表情包: 12种情感表达

## 3. 环境场景设计

### 3.1 关卡背景设计

#### 第一章：废墟觉醒
**环境描述**: 城市废墟，建筑残骸
**视觉元素**:
- 倒塌的建筑物轮廓
- 生锈的车辆残骸
- 破碎的道路和管道
- 稀疏的枯死植物

**色彩方案**: 灰棕色主调+局部红色危险区域
**光照设计**: 阴沉的天空+微弱的阳光透射

#### 第二章：工业遗迹
**环境描述**: 废弃工厂，机械设备
**视觉元素**:
- 巨大的工业机械
- 生锈的管道系统
- 废弃的传送带
- 泄漏的化学物质

**色彩方案**: 金属灰+锈色+工业蓝
**光照设计**: 冷色调工业照明+蒸汽效果

#### 第三章：核心污染区
**环境描述**: 核电站废墟，高辐射区
**视觉元素**:
- 破损的反应堆建筑
- 辐射警告标识
- 绿色辐射光效
- 变异的植物形态

**色彩方案**: 绿色辐射光+黄色警告+黑色阴影
**光照设计**: 诡异的绿色光源+闪烁效果

### 3.2 基地场景设计

#### 指挥中心
**建筑风格**: 现代科技+环保理念
**外观特征**:
- 圆形主体建筑
- 太阳能板屋顶
- 全景玻璃墙面
- 绿色植物装饰

**内部设计**:
- 全息显示屏
- 环形控制台
- 生态监测设备
- 舒适的休息区域

#### 资源处理厂
**建筑风格**: 工业功能性+清洁技术
**外观特征**:
- 方形厂房结构
- 净化塔和烟囱
- 传送带系统
- 废料分拣区域

#### 居住区
**建筑风格**: 生态友好+人性化设计
**外观特征**:
- 模块化住宅单元
- 垂直花园系统
- 公共活动空间
- 可再生能源设施

## 4. UI视觉设计

### 4.1 界面风格

**设计语言**: 科技简约+环保元素
**视觉特征**:
- 圆角矩形为主要形状语言
- 渐变色彩+微妙阴影
- 图标线性设计+填充色
- 动效流畅自然

### 4.2 图标设计规范

#### 资源图标
**金属图标**:
```
    ╭─────╮
   ╱       ╲
  ╱  ┌───┐  ╲  ← 金属光泽效果
 ╱   │ M │   ╲ ← 字母标识
╱    └───┘    ╲
╲             ╱
 ╲___________╱
```

**塑料图标**:
```
    ○○○○○
   ○     ○
  ○   P   ○  ← 半透明效果
 ○         ○ ← 彩色渐变
○___________○
```

#### 功能图标
**设置图标**: 齿轮形状+科技线条
**商店图标**: 购物袋+金币装饰
**成就图标**: 奖杯+星光效果
**好友图标**: 双人剪影+连接线

### 4.3 按钮设计

#### 主要按钮
**正常状态**:
- 背景: 科技蓝渐变 (#1E90FF → #4169E1)
- 边框: 2px 亮蓝色描边
- 文字: 白色，粗体
- 阴影: 0 4px 8px rgba(30,144,255,0.3)

**悬停状态**:
- 背景: 亮度增加20%
- 缩放: 105%
- 阴影: 增强至 0 6px 12px

**点击状态**:
- 缩放: 95%
- 阴影: 减弱至 0 2px 4px

#### 次要按钮
**样式**: 透明背景+边框+文字
**颜色**: 中性灰色系
**交互**: 悬停时填充背景色

## 5. 特效设计

### 5.1 粒子特效

#### 消除特效
**基础消除**:
- 粒子类型: 小型光点+废料碎片
- 运动轨迹: 向上飘散+旋转
- 颜色: 根据废料类型变化
- 持续时间: 0.5-0.8秒

**连锁消除**:
- 粒子类型: 能量波+光环扩散
- 运动轨迹: 从中心向外扩散
- 颜色: 彩虹渐变效果
- 持续时间: 1.0-1.5秒

**特殊消除**:
- 爆炸效果: 火花四射+冲击波
- 净化效果: 绿色光芒+治愈粒子
- 变异效果: 紫色扭曲+不稳定闪烁

#### 环境特效
**污染效果**:
- 毒雾: 绿色半透明雾气缓慢流动
- 腐蚀: 表面冒泡+颜色变化
- 辐射: 绿色光线+粒子流

**净化效果**:
- 清洁波: 蓝白色光波扫过
- 植物生长: 绿色新芽+花朵绽放
- 空气净化: 清新粒子+光点闪烁

### 5.2 UI动效

#### 页面转场
**滑入效果**: 新页面从右侧滑入，旧页面向左滑出
**淡入效果**: 透明度从0到1，配合轻微缩放
**弹性效果**: 超出目标位置后回弹

#### 元素动画
**按钮反馈**: 点击时缩放+颜色变化
**图标动画**: 悬停时轻微浮动+发光
**数值变化**: 数字滚动+颜色闪烁

## 6. 音效设计规范

### 6.1 音效分类

#### 环境音效
**废土环境**: 风声+远处机械声+偶尔的金属碰撞
**工业环境**: 机械运转+蒸汽声+电流声
**核污染环境**: 盖革计数器+低频嗡鸣+电子干扰

#### 游戏音效
**消除音效**:
- 金属: 清脆的金属碰撞声
- 塑料: 轻快的破裂声
- 玻璃: 清脆的破碎声
- 有机: 湿润的挤压声

**建造音效**:
- 设施建造: 机械组装声+能量充电
- 升级完成: 成功提示音+能量释放
- 设施运转: 持续的机械运转声

### 6.2 音乐设计

#### 主题音乐
**主旋律**: 希望与重生的主题
**乐器配置**: 
- 主旋律: 钢琴+弦乐
- 节奏: 电子鼓+合成器
- 装饰: 环境音效+科技音色

**情感曲线**: 
- 开始: 略带忧郁，体现废土现状
- 发展: 逐渐明亮，展现希望
- 高潮: 激昂有力，象征重建成功

#### 关卡音乐
**废土关卡**: 低沉神秘+希望暗示
**工业关卡**: 机械节奏+电子元素
**核污染关卡**: 紧张诡异+电子失真
**Boss战**: 激烈战斗+管弦乐高潮

## 7. 技术规范

### 7.1 资源规格

#### 纹理规格
**UI纹理**: 
- 格式: PNG (支持透明)
- 尺寸: 2的幂次方 (512x512, 1024x1024)
- 压缩: ASTC (移动端) / DXT (PC端)

**游戏纹理**:
- 格式: TGA/PNG
- 尺寸: 根据重要性分级 (128x128 到 2048x2048)
- Mipmap: 开启，提高远距离渲染质量

#### 模型规格
**废料块模型**:
- 面数: 100-500三角面
- UV展开: 单张贴图，充分利用空间
- LOD: 3个等级 (高中低精度)

**建筑模型**:
- 面数: 1000-5000三角面
- 模块化设计: 便于组合和变化
- 材质球: 最多4个材质

### 7.2 性能优化

#### 渲染优化
**批处理**: 相同材质的对象合并渲染
**遮挡剔除**: 不可见对象不参与渲染
**动态合批**: 小对象动态合并减少Draw Call

#### 内存优化
**纹理压缩**: 根据平台选择最优压缩格式
**音频压缩**: 使用OGG格式，平衡质量和大小
**资源流式加载**: 按需加载，及时释放

## 8. 制作流程

### 8.1 概念设计阶段
1. **风格探索**: 创建多种风格的概念图
2. **色彩定义**: 确定主要色彩方案
3. **关键视觉**: 设计标志性的视觉元素
4. **风格指南**: 制作详细的美术规范文档

### 8.2 资产制作阶段
1. **建模制作**: 按照规范创建3D模型
2. **纹理绘制**: 制作高质量的材质贴图
3. **动画制作**: 创建流畅的动画效果
4. **特效制作**: 设计各种粒子特效

### 8.3 整合测试阶段
1. **引擎整合**: 将资产导入游戏引擎
2. **效果调试**: 调整光照、材质参数
3. **性能测试**: 确保在目标设备上流畅运行
4. **视觉优化**: 根据实际效果进行微调

---

*美术设计是游戏的视觉灵魂，通过统一的风格规范和精心的视觉设计，为玩家创造沉浸式的游戏体验。每个视觉元素都应该服务于游戏的核心主题，传达希望与重生的正能量价值观。*
