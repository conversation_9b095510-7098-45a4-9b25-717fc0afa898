using System;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    // Event definitions
    public class GameEvent { }

    public class LevelStartEvent : GameEvent
    {
        public LevelData levelData;
        public LevelStartEvent(LevelData data) { levelData = data; }
    }

    public class LevelCompleteEvent : GameEvent
    {
        public LevelData levelData;
        public int finalScore;
        public float timeRemaining;
        public int stepsUsed;
        public bool isPerfect;

        public LevelCompleteEvent(LevelData data) { levelData = data; }
    }

    public class GameOverEvent : GameEvent { }

    public class MatchEvent : GameEvent
    {
        public List<Vector2Int> matchedBlocks;
        public Dictionary<ResourceType, int> resources;
        public int combo;
        public WasteBlockType matchType;
        public int score;
        public Vector3 position;
    }

    public class EnemySpawnEvent : GameEvent
    {
        public Enemy enemy;
        public Vector2Int spawnPosition;
    }

    public class EnemyReachedEvent : GameEvent
    {
        public Enemy enemy;
        public int pollutionDamage;
    }

    public class EnemyDefeatedEvent : GameEvent
    {
        public Enemy enemy;
        public Vector2Int position;
    }

    public class TowerBuiltEvent : GameEvent
    {
        public TowerType towerType;
        public Vector2Int position;
        public Dictionary<ResourceType, int> cost;
    }

    public class TowerUpgradedEvent : GameEvent
    {
        public Tower tower;
        public int newLevel;
    }

    public class ResourceChangedEvent : GameEvent
    {
        public ResourceType resourceType;
        public int amount;
        public int newTotal;
    }

    public class PollutionChangedEvent : GameEvent
    {
        public int oldValue;
        public int newValue;
        public int change;
    }

    public class ComboEvent : GameEvent
    {
        public int comboCount;
        public float multiplier;
    }

    public class ScoreEvent : GameEvent
    {
        public int points;
        public Vector3 position;
    }

    public class ResourceGainEvent : GameEvent
    {
        public Dictionary<ResourceType, int> resources;
    }

    public class GameStateChangeEvent : GameEvent
    {
        public GameState newState;
    }

    public class SpecialEffectEvent : GameEvent
    {
        public SpecialEffectType effectType;
        public Vector2Int position;
        public List<Vector2Int> affectedPositions;
    }

    public class BuildingConstructedEvent : GameEvent
    {
        public BuildingType buildingType;
        public int level;
    }

    public class TechnologyResearchedEvent : GameEvent
    {
        public TechnologyType technologyType;
        public int level;
    }

    // Event System implementation
    public static class EventSystem
    {
        private static Dictionary<Type, List<Delegate>> eventHandlers = new Dictionary<Type, List<Delegate>>();

        public static void Subscribe<T>(Action<T> handler) where T : GameEvent
        {
            Type eventType = typeof(T);
            
            if (!eventHandlers.ContainsKey(eventType))
            {
                eventHandlers[eventType] = new List<Delegate>();
            }
            
            eventHandlers[eventType].Add(handler);
        }

        public static void Unsubscribe<T>(Action<T> handler) where T : GameEvent
        {
            Type eventType = typeof(T);
            
            if (eventHandlers.ContainsKey(eventType))
            {
                eventHandlers[eventType].Remove(handler);
                
                if (eventHandlers[eventType].Count == 0)
                {
                    eventHandlers.Remove(eventType);
                }
            }
        }

        public static void Publish<T>(T gameEvent) where T : GameEvent
        {
            Type eventType = typeof(T);
            
            if (eventHandlers.ContainsKey(eventType))
            {
                foreach (var handler in eventHandlers[eventType])
                {
                    try
                    {
                        ((Action<T>)handler).Invoke(gameEvent);
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"Error handling event {eventType.Name}: {e.Message}");
                    }
                }
            }
        }

        public static void Clear()
        {
            eventHandlers.Clear();
        }

        public static void Clear<T>() where T : GameEvent
        {
            Type eventType = typeof(T);
            if (eventHandlers.ContainsKey(eventType))
            {
                eventHandlers.Remove(eventType);
            }
        }

        public static int GetSubscriberCount<T>() where T : GameEvent
        {
            Type eventType = typeof(T);
            return eventHandlers.ContainsKey(eventType) ? eventHandlers[eventType].Count : 0;
        }
    }

    // Service Locator pattern for easy access to systems
    public static class ServiceLocator
    {
        private static Dictionary<Type, object> services = new Dictionary<Type, object>();

        public static void Register<T>(T service)
        {
            Type serviceType = typeof(T);
            services[serviceType] = service;
        }

        public static T Get<T>()
        {
            Type serviceType = typeof(T);
            if (services.ContainsKey(serviceType))
            {
                return (T)services[serviceType];
            }
            
            Debug.LogWarning($"Service of type {serviceType.Name} not found!");
            return default(T);
        }

        public static bool IsRegistered<T>()
        {
            return services.ContainsKey(typeof(T));
        }

        public static void Unregister<T>()
        {
            Type serviceType = typeof(T);
            if (services.ContainsKey(serviceType))
            {
                services.Remove(serviceType);
            }
        }

        public static void Clear()
        {
            services.Clear();
        }
    }

    // Object Pool for performance optimization
    public class ObjectPool<T> where T : MonoBehaviour
    {
        private Queue<T> pool = new Queue<T>();
        private T prefab;
        private Transform parent;
        private int initialSize;

        public ObjectPool(T prefab, int initialSize = 10, Transform parent = null)
        {
            this.prefab = prefab;
            this.initialSize = initialSize;
            this.parent = parent;
            
            InitializePool();
        }

        private void InitializePool()
        {
            for (int i = 0; i < initialSize; i++)
            {
                T obj = UnityEngine.Object.Instantiate(prefab, parent);
                obj.gameObject.SetActive(false);
                pool.Enqueue(obj);
            }
        }

        public T Get()
        {
            if (pool.Count > 0)
            {
                T obj = pool.Dequeue();
                obj.gameObject.SetActive(true);
                return obj;
            }
            else
            {
                T obj = UnityEngine.Object.Instantiate(prefab, parent);
                return obj;
            }
        }

        public void Return(T obj)
        {
            if (obj != null)
            {
                obj.gameObject.SetActive(false);
                pool.Enqueue(obj);
            }
        }

        public void Clear()
        {
            while (pool.Count > 0)
            {
                T obj = pool.Dequeue();
                if (obj != null)
                {
                    UnityEngine.Object.Destroy(obj.gameObject);
                }
            }
        }

        public int AvailableCount => pool.Count;
    }

    // Utility class for common game calculations
    public static class GameUtils
    {
        public static Vector2Int[] GetDirections()
        {
            return new Vector2Int[]
            {
                Vector2Int.up,
                Vector2Int.down,
                Vector2Int.left,
                Vector2Int.right
            };
        }

        public static Vector2Int[] GetAllDirections()
        {
            return new Vector2Int[]
            {
                Vector2Int.up,
                Vector2Int.down,
                Vector2Int.left,
                Vector2Int.right,
                new Vector2Int(1, 1),
                new Vector2Int(1, -1),
                new Vector2Int(-1, 1),
                new Vector2Int(-1, -1)
            };
        }

        public static bool IsValidGridPosition(Vector2Int position, int width, int height)
        {
            return position.x >= 0 && position.x < width && position.y >= 0 && position.y < height;
        }

        public static float CalculateDistance(Vector2Int a, Vector2Int b)
        {
            return Vector2Int.Distance(a, b);
        }

        public static int CalculateManhattanDistance(Vector2Int a, Vector2Int b)
        {
            return Mathf.Abs(a.x - b.x) + Mathf.Abs(a.y - b.y);
        }

        public static List<Vector2Int> GetNeighbors(Vector2Int position, int width, int height)
        {
            List<Vector2Int> neighbors = new List<Vector2Int>();
            
            foreach (Vector2Int direction in GetDirections())
            {
                Vector2Int neighbor = position + direction;
                if (IsValidGridPosition(neighbor, width, height))
                {
                    neighbors.Add(neighbor);
                }
            }
            
            return neighbors;
        }

        public static List<Vector2Int> GetPositionsInRange(Vector2Int center, int range, int width, int height)
        {
            List<Vector2Int> positions = new List<Vector2Int>();
            
            for (int x = center.x - range; x <= center.x + range; x++)
            {
                for (int y = center.y - range; y <= center.y + range; y++)
                {
                    Vector2Int pos = new Vector2Int(x, y);
                    if (IsValidGridPosition(pos, width, height) && 
                        CalculateManhattanDistance(center, pos) <= range)
                    {
                        positions.Add(pos);
                    }
                }
            }
            
            return positions;
        }

        public static Color GetResourceColor(ResourceType resourceType)
        {
            switch (resourceType)
            {
                case ResourceType.Metal: return new Color(0.7f, 0.7f, 0.8f); // Silver
                case ResourceType.Plastic: return new Color(0.2f, 0.6f, 1f); // Blue
                case ResourceType.Wood: return new Color(0.6f, 0.4f, 0.2f); // Brown
                case ResourceType.Electronic: return new Color(0.2f, 0.8f, 0.2f); // Green
                case ResourceType.Glass: return new Color(0.9f, 0.9f, 0.9f, 0.7f); // Transparent
                case ResourceType.Organic: return new Color(1f, 0.8f, 0.2f); // Yellow
                case ResourceType.CoreFragment: return new Color(1f, 0.2f, 0.8f); // Pink
                case ResourceType.Core: return new Color(0.8f, 0.2f, 1f); // Purple
                default: return Color.white;
            }
        }

        public static string FormatTime(float timeInSeconds)
        {
            int minutes = Mathf.FloorToInt(timeInSeconds / 60);
            int seconds = Mathf.FloorToInt(timeInSeconds % 60);
            return $"{minutes:00}:{seconds:00}";
        }

        public static string FormatNumber(int number)
        {
            if (number >= 1000000)
                return $"{number / 1000000f:F1}M";
            else if (number >= 1000)
                return $"{number / 1000f:F1}K";
            else
                return number.ToString();
        }
    }
}
