using System.Collections;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 高品质游戏启动器 - 整合所有高级功能
    /// </summary>
    public class PremiumGameStarter : MonoBehaviour
    {
        [Header("启动设置")]
        public bool autoStart = true;
        public bool enablePremiumFeatures = true;
        public bool createHDBackground = true;

        [Header("游戏配置")]
        public int gridSize = 8;
        public float cellSize = 1.0f;
        public int initialWasteBlocks = 20;

        [Header("视觉设置")]
        public bool enableParticleEffects = true;
        public bool enableScreenEffects = true;
        public bool enableDynamicLighting = true;

        private bool isInitialized = false;

        private void Start()
        {
            if (autoStart)
            {
                StartCoroutine(InitializePremiumGame());
            }
        }

        private IEnumerator InitializePremiumGame()
        {
            Debug.Log("=== 启动高品质废土再生游戏 ===");

            // 1. 创建高品质环境
            yield return StartCoroutine(CreatePremiumEnvironment());

            // 2. 设置高级相机系统
            yield return StartCoroutine(SetupAdvancedCamera());

            // 3. 创建核心游戏系统
            yield return StartCoroutine(CreateGameSystems());

            // 4. 创建现代化UI
            yield return StartCoroutine(CreateModernUI());

            // 5. 设置视觉效果
            yield return StartCoroutine(SetupVisualEffects());

            // 6. 创建高品质游戏内容
            yield return StartCoroutine(CreatePremiumContent());

            // 7. 启动游戏
            StartPremiumGame();

            isInitialized = true;
            Debug.Log("=== 高品质游戏启动完成！===");
        }

        private IEnumerator CreatePremiumEnvironment()
        {
            Debug.Log("创建高品质环境...");

            // 创建HD背景
            if (createHDBackground)
            {
                CreateHDBackground();
            }

            // 设置高级光照
            if (enableDynamicLighting)
            {
                SetupDynamicLighting();
            }

            // 创建环境装饰
            CreateEnvironmentDecorations();

            yield return new WaitForSeconds(0.2f);
        }

        private void CreateHDBackground()
        {
            // 创建渐变天空盒
            GameObject skybox = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            skybox.name = "HDSkybox";
            skybox.transform.localScale = Vector3.one * 100f;

            // 反转法线，让内部可见
            MeshRenderer renderer = skybox.GetComponent<MeshRenderer>();
            Material skyMaterial = new Material(Shader.Find("Unlit/Texture"));
            
            // 创建渐变纹理
            Texture2D gradientTexture = CreateGradientTexture();
            skyMaterial.mainTexture = gradientTexture;
            renderer.material = skyMaterial;

            // 移除碰撞器
            Destroy(skybox.GetComponent<Collider>());

            // 创建地面
            CreatePremiumGround();

            // 创建远景装饰
            CreateDistantDecorations();
        }

        private Texture2D CreateGradientTexture()
        {
            int width = 256;
            int height = 256;
            Texture2D texture = new Texture2D(width, height);

            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    float t = (float)y / height;
                    
                    // 废土风格的渐变：从暗红到橙黄
                    Color bottomColor = new Color(0.3f, 0.1f, 0.1f); // 暗红
                    Color topColor = new Color(0.8f, 0.6f, 0.3f);    // 橙黄
                    
                    Color pixelColor = Color.Lerp(bottomColor, topColor, t);
                    texture.SetPixel(x, y, pixelColor);
                }
            }

            texture.Apply();
            return texture;
        }

        private void CreatePremiumGround()
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "PremiumGround";
            ground.transform.position = new Vector3(gridSize * 0.5f, -0.5f, gridSize * 0.5f);
            ground.transform.localScale = new Vector3(gridSize * 0.15f, 1, gridSize * 0.15f);

            // 创建废土材质
            Material groundMaterial = new Material(Shader.Find("Standard"));
            groundMaterial.color = new Color(0.4f, 0.3f, 0.2f);
            groundMaterial.metallic = 0.1f;
            groundMaterial.smoothness = 0.2f;

            ground.GetComponent<Renderer>().material = groundMaterial;
        }

        private void CreateDistantDecorations()
        {
            // 创建远处的废墟建筑
            for (int i = 0; i < 8; i++)
            {
                GameObject building = GameObject.CreatePrimitive(PrimitiveType.Cube);
                building.name = $"DistantBuilding_{i}";
                
                float angle = i * 45f;
                float distance = 25f;
                Vector3 position = new Vector3(
                    Mathf.Sin(angle * Mathf.Deg2Rad) * distance,
                    Random.Range(2f, 8f),
                    Mathf.Cos(angle * Mathf.Deg2Rad) * distance
                );
                
                building.transform.position = position;
                building.transform.localScale = new Vector3(
                    Random.Range(1f, 3f),
                    Random.Range(3f, 10f),
                    Random.Range(1f, 3f)
                );

                // 废墟材质
                Material buildingMaterial = new Material(Shader.Find("Standard"));
                buildingMaterial.color = new Color(0.3f, 0.3f, 0.3f);
                building.GetComponent<Renderer>().material = buildingMaterial;
            }
        }

        private void SetupDynamicLighting()
        {
            // 主光源 - 模拟废土中的昏暗阳光
            GameObject mainLight = new GameObject("MainLight");
            Light light = mainLight.AddComponent<Light>();
            light.type = LightType.Directional;
            light.color = new Color(1f, 0.8f, 0.6f); // 暖色调
            light.intensity = 1.2f;
            light.shadows = LightShadows.Soft;
            
            mainLight.transform.eulerAngles = new Vector3(45f, 30f, 0f);

            // 环境光
            RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
            RenderSettings.ambientSkyColor = new Color(0.5f, 0.4f, 0.3f);
            RenderSettings.ambientEquatorColor = new Color(0.3f, 0.3f, 0.3f);
            RenderSettings.ambientGroundColor = new Color(0.2f, 0.2f, 0.2f);

            // 添加动态光照效果
            StartCoroutine(AnimateLighting());
        }

        private IEnumerator AnimateLighting()
        {
            Light mainLight = FindObjectOfType<Light>();
            if (mainLight == null) yield break;

            float baseIntensity = mainLight.intensity;
            
            while (true)
            {
                float time = Time.time;
                float flicker = Mathf.Sin(time * 2f) * 0.1f + Mathf.Sin(time * 5f) * 0.05f;
                mainLight.intensity = baseIntensity + flicker;
                
                yield return new WaitForSeconds(0.1f);
            }
        }

        private void CreateEnvironmentDecorations()
        {
            // 创建废料堆
            for (int i = 0; i < 5; i++)
            {
                CreateWastePile(new Vector3(
                    Random.Range(-5f, gridSize + 5f),
                    0f,
                    Random.Range(-5f, gridSize + 5f)
                ));
            }

            // 创建破损的管道
            for (int i = 0; i < 3; i++)
            {
                CreateBrokenPipe(new Vector3(
                    Random.Range(-3f, gridSize + 3f),
                    0.5f,
                    Random.Range(-3f, gridSize + 3f)
                ));
            }
        }

        private void CreateWastePile(Vector3 position)
        {
            GameObject pile = new GameObject("WastePile");
            pile.transform.position = position;

            for (int i = 0; i < Random.Range(3, 8); i++)
            {
                GameObject piece = GameObject.CreatePrimitive(PrimitiveType.Cube);
                piece.transform.SetParent(pile.transform);
                piece.transform.localPosition = new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(0f, 2f),
                    Random.Range(-1f, 1f)
                );
                piece.transform.localScale = Vector3.one * Random.Range(0.3f, 0.8f);
                piece.transform.rotation = Random.rotation;

                Material wasteMaterial = new Material(Shader.Find("Standard"));
                wasteMaterial.color = new Color(
                    Random.Range(0.2f, 0.6f),
                    Random.Range(0.2f, 0.4f),
                    Random.Range(0.1f, 0.3f)
                );
                piece.GetComponent<Renderer>().material = wasteMaterial;
            }
        }

        private void CreateBrokenPipe(Vector3 position)
        {
            GameObject pipe = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            pipe.name = "BrokenPipe";
            pipe.transform.position = position;
            pipe.transform.localScale = new Vector3(0.5f, 2f, 0.5f);
            pipe.transform.rotation = Quaternion.Euler(Random.Range(-30f, 30f), Random.Range(0f, 360f), Random.Range(-30f, 30f));

            Material pipeMaterial = new Material(Shader.Find("Standard"));
            pipeMaterial.color = new Color(0.4f, 0.4f, 0.4f);
            pipeMaterial.metallic = 0.8f;
            pipeMaterial.smoothness = 0.3f;
            pipe.GetComponent<Renderer>().material = pipeMaterial;
        }

        private IEnumerator SetupAdvancedCamera()
        {
            Debug.Log("设置高级相机系统...");

            Camera mainCamera = Camera.main;
            if (mainCamera == null)
            {
                GameObject cameraObj = new GameObject("PremiumCamera");
                mainCamera = cameraObj.AddComponent<Camera>();
                cameraObj.tag = "MainCamera";
            }

            // 设置相机参数
            mainCamera.transform.position = new Vector3(gridSize * 0.5f, 12f, -8f);
            mainCamera.transform.eulerAngles = new Vector3(45f, 0f, 0f);
            mainCamera.fieldOfView = 60f;
            mainCamera.farClipPlane = 100f;

            // 添加高级相机控制
            if (mainCamera.GetComponent<AdvancedCameraController>() == null)
            {
                mainCamera.gameObject.AddComponent<AdvancedCameraController>();
            }

            yield return new WaitForSeconds(0.1f);
        }

        private IEnumerator CreateGameSystems()
        {
            Debug.Log("创建游戏系统...");

            // 创建GameManager
            if (GameManager.Instance == null)
            {
                GameObject gameManagerObj = new GameObject("GameManager");
                gameManagerObj.AddComponent<GameManager>();
            }

            // 创建增强的网格系统
            GameObject gridSystemObj = new GameObject("EnhancedGridSystem");
            GridSystem gridSystem = gridSystemObj.AddComponent<GridSystem>();
            gridSystem.gridWidth = gridSize;
            gridSystem.gridHeight = gridSize;
            gridSystem.cellSize = cellSize;

            // 创建增强的消除系统
            GameObject matchSystemObj = new GameObject("EnhancedMatchSystem");
            matchSystemObj.AddComponent<EnhancedMatchSystem>();

            // 创建其他系统
            CreateOtherSystems();

            yield return new WaitForSeconds(0.2f);
        }

        private void CreateOtherSystems()
        {
            // TowerSystem
            GameObject towerSystemObj = new GameObject("TowerSystem");
            towerSystemObj.AddComponent<TowerSystem>();

            // EnemySystem
            GameObject enemySystemObj = new GameObject("EnemySystem");
            enemySystemObj.AddComponent<EnemySystem>();

            // AudioManager
            GameObject audioManagerObj = new GameObject("AudioManager");
            audioManagerObj.AddComponent<AudioManager>();

            // GameplayEnhancer - 新增的玩法增强系统
            GameObject enhancerObj = new GameObject("GameplayEnhancer");
            enhancerObj.AddComponent<GameplayEnhancer>();
        }

        private IEnumerator CreateModernUI()
        {
            Debug.Log("创建现代化UI...");

            GameObject uiManagerObj = new GameObject("ModernUIManager");
            uiManagerObj.AddComponent<ModernUIManager>();

            yield return new WaitForSeconds(0.1f);
        }

        private IEnumerator SetupVisualEffects()
        {
            if (!enablePremiumFeatures) yield break;

            Debug.Log("设置视觉效果...");

            GameObject effectsManagerObj = new GameObject("VisualEffectsManager");
            effectsManagerObj.AddComponent<VisualEffectsManager>();

            yield return new WaitForSeconds(0.1f);
        }

        private IEnumerator CreatePremiumContent()
        {
            Debug.Log("创建高品质游戏内容...");

            // 初始化网格系统
            GridSystem gridSystem = FindObjectOfType<GridSystem>();
            if (gridSystem != null)
            {
                gridSystem.Initialize();

                // 创建高品质的测试关卡
                yield return StartCoroutine(CreatePremiumTestLevel());
            }

            yield return new WaitForSeconds(0.2f);
        }

        private IEnumerator CreatePremiumTestLevel()
        {
            LevelData premiumLevel = new LevelData
            {
                levelId = 1,
                levelName = "废土净化试验场",
                chapterName = "重建希望",
                difficulty = 1,
                gridSize = new Vector2Int(gridSize, gridSize),
                initialPollution = 80,
                targetPollution = 15,
                stepLimit = 40,
                timeLimit = 0f,
                purifierPosition = new Vector2Int(gridSize - 1, 0)
            };

            // 创建平衡的废料块分布
            CreateBalancedWasteDistribution(premiumLevel);

            // 添加敌人波次
            CreateEnemyWaves(premiumLevel);

            // 设置奖励
            SetupLevelRewards(premiumLevel);

            // 启动关卡
            if (GameManager.Instance != null)
            {
                GameManager.Instance.StartLevel(premiumLevel);
            }

            yield return null;
        }

        private void CreateBalancedWasteDistribution(LevelData level)
        {
            // 确保每种类型的废料块都有合理的分布
            WasteBlockType[] types = {
                WasteBlockType.Metal, WasteBlockType.Plastic, WasteBlockType.Wood,
                WasteBlockType.Electronic, WasteBlockType.Glass, WasteBlockType.Organic
            };

            for (int i = 0; i < initialWasteBlocks; i++)
            {
                WasteBlockType type = types[i % types.Length];
                Vector2Int position;
                
                // 确保位置不重复
                do
                {
                    position = new Vector2Int(
                        Random.Range(0, gridSize - 2),
                        Random.Range(1, gridSize)
                    );
                } while (level.wasteBlocks.Exists(w => w.position == position));

                level.wasteBlocks.Add(new WasteBlockData
                {
                    type = type,
                    position = position,
                    pollutionValue = Random.Range(2, 5)
                });
            }
        }

        private void CreateEnemyWaves(LevelData level)
        {
            // 第一波 - 简单的爬行者
            EnemyWaveData wave1 = new EnemyWaveData
            {
                spawnTime = 10f,
                enemyType = EnemyType.PollutionCrawler,
                count = 3,
                spawnInterval = 2f
            };
            wave1.spawnPositions.Add(new Vector2Int(0, gridSize / 2));
            for (int x = 1; x < gridSize; x++)
            {
                wave1.path.Add(new Vector2Int(x, gridSize / 2));
            }
            level.enemyWaves.Add(wave1);

            // 第二波 - 飞行单位
            EnemyWaveData wave2 = new EnemyWaveData
            {
                spawnTime = 25f,
                enemyType = EnemyType.CorrosiveFly,
                count = 2,
                spawnInterval = 3f
            };
            wave2.spawnPositions.Add(new Vector2Int(0, gridSize - 1));
            wave2.path.Add(new Vector2Int(gridSize - 1, 0));
            level.enemyWaves.Add(wave2);
        }

        private void SetupLevelRewards(LevelData level)
        {
            level.basicReward[ResourceType.Metal] = 30;
            level.basicReward[ResourceType.Plastic] = 25;
            level.basicReward[ResourceType.Wood] = 20;
            level.basicReward[ResourceType.Electronic] = 15;
            level.basicReward[ResourceType.Core] = 5;

            level.perfectReward[ResourceType.Metal] = 50;
            level.perfectReward[ResourceType.Plastic] = 40;
            level.perfectReward[ResourceType.Core] = 10;
        }

        private void StartPremiumGame()
        {
            // 连接所有系统
            ConnectAllSystems();

            // 设置初始游戏状态
            if (GameManager.Instance != null)
            {
                GameManager.Instance.ChangeGameState(GameState.Playing);
            }

            Debug.Log("高品质游戏体验已启动！");
            Debug.Log("操作指南：");
            Debug.Log("- 点击相同颜色的废料块进行消除（至少3个连接）");
            Debug.Log("- 获得资源后可以建造防御塔");
            Debug.Log("- 目标：将污染度降低到目标值以下");
            Debug.Log("- 右键拖拽旋转视角，滚轮缩放");
        }

        private void ConnectAllSystems()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.gridSystem = FindObjectOfType<GridSystem>();
                GameManager.Instance.matchSystem = FindObjectOfType<MatchSystem>();
                GameManager.Instance.towerSystem = FindObjectOfType<TowerSystem>();
                GameManager.Instance.enemySystem = FindObjectOfType<EnemySystem>();
                GameManager.Instance.uiManager = FindObjectOfType<UIManager>();
                GameManager.Instance.audioManager = FindObjectOfType<AudioManager>();

                // 初始化所有系统
                GameManager.Instance.gridSystem?.Initialize();
                GameManager.Instance.matchSystem?.Initialize();
                GameManager.Instance.towerSystem?.Initialize();
                GameManager.Instance.enemySystem?.Initialize();
                GameManager.Instance.uiManager?.Initialize();
                GameManager.Instance.audioManager?.Initialize();

                // 初始化增强系统
                FindObjectOfType<EnhancedMatchSystem>()?.Initialize();
            }
        }

        private void Update()
        {
            if (!isInitialized) return;

            // 快捷键
            if (Input.GetKeyDown(KeyCode.R))
            {
                RestartLevel();
            }

            if (Input.GetKeyDown(KeyCode.P))
            {
                TogglePause();
            }
        }

        private void RestartLevel()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.RestartLevel();
            }
        }

        private void TogglePause()
        {
            if (GameManager.Instance != null)
            {
                if (GameManager.Instance.currentState == GameState.Playing)
                {
                    GameManager.Instance.ChangeGameState(GameState.Paused);
                }
                else if (GameManager.Instance.currentState == GameState.Paused)
                {
                    GameManager.Instance.ChangeGameState(GameState.Playing);
                }
            }
        }
    }

    /// <summary>
    /// 高级相机控制器
    /// </summary>
    public class AdvancedCameraController : MonoBehaviour
    {
        [Header("控制设置")]
        public float rotationSpeed = 100f;
        public float zoomSpeed = 5f;
        public float panSpeed = 2f;

        [Header("限制")]
        public float minZoom = 5f;
        public float maxZoom = 20f;
        public float minAngle = 10f;
        public float maxAngle = 80f;

        private Vector3 lastMousePosition;
        private float currentZoom;
        private float currentXRotation;

        private void Start()
        {
            currentZoom = Vector3.Distance(transform.position, Vector3.zero);
            currentXRotation = transform.eulerAngles.x;
        }

        private void Update()
        {
            HandleInput();
        }

        private void HandleInput()
        {
            // 右键旋转
            if (Input.GetMouseButtonDown(1))
            {
                lastMousePosition = Input.mousePosition;
            }

            if (Input.GetMouseButton(1))
            {
                Vector3 deltaMousePosition = Input.mousePosition - lastMousePosition;
                
                // 水平旋转
                transform.RotateAround(Vector3.zero, Vector3.up, deltaMousePosition.x * rotationSpeed * Time.deltaTime);
                
                // 垂直旋转
                currentXRotation -= deltaMousePosition.y * rotationSpeed * Time.deltaTime;
                currentXRotation = Mathf.Clamp(currentXRotation, minAngle, maxAngle);
                
                Vector3 eulerAngles = transform.eulerAngles;
                eulerAngles.x = currentXRotation;
                transform.eulerAngles = eulerAngles;

                lastMousePosition = Input.mousePosition;
            }

            // 滚轮缩放
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (scroll != 0f)
            {
                currentZoom -= scroll * zoomSpeed;
                currentZoom = Mathf.Clamp(currentZoom, minZoom, maxZoom);
                
                Vector3 direction = transform.position.normalized;
                transform.position = direction * currentZoom;
            }

            // 中键平移
            if (Input.GetMouseButton(2))
            {
                Vector3 deltaMousePosition = Input.mousePosition - lastMousePosition;
                
                Vector3 right = transform.right;
                Vector3 up = Vector3.Cross(right, transform.forward);
                
                Vector3 panMovement = (-right * deltaMousePosition.x + up * deltaMousePosition.y) * panSpeed * Time.deltaTime;
                transform.position += panMovement;

                lastMousePosition = Input.mousePosition;
            }

            if (Input.GetMouseButtonDown(2))
            {
                lastMousePosition = Input.mousePosition;
            }
        }
    }
}
