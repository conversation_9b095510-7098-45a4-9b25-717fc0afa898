using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 最简化的游戏启动器 - 确保能够编译和运行
    /// </summary>
    public class MinimalStarter : MonoBehaviour
    {
        [Header("基础设置")]
        public bool autoStart = true;

        private void Start()
        {
            if (autoStart)
            {
                InitializeBasicGame();
            }
        }

        private void InitializeBasicGame()
        {
            Debug.Log("=== 开始初始化最简化游戏 ===");

            // 1. 设置相机
            SetupCamera();

            // 2. 创建GameManager
            CreateGameManager();

            // 3. 创建基础系统
            CreateBasicSystems();

            Debug.Log("=== 初始化完成！按T键测试 ===");
        }

        private void SetupCamera()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
            {
                GameObject cameraObj = new GameObject("Main Camera");
                mainCamera = cameraObj.AddComponent<Camera>();
                cameraObj.tag = "MainCamera";
            }

            mainCamera.transform.position = new Vector3(4, 10, -8);
            mainCamera.transform.eulerAngles = new Vector3(45, 0, 0);
            Debug.Log("✓ 相机设置完成");
        }

        private void CreateGameManager()
        {
            if (GameManager.Instance == null)
            {
                GameObject gameManagerObj = new GameObject("GameManager");
                gameManagerObj.AddComponent<GameManager>();
                Debug.Log("✓ GameManager 创建完成");
            }
        }

        private void CreateBasicSystems()
        {
            // 只创建最基础的系统，避免复杂的依赖
            if (FindObjectOfType<GridSystem>() == null)
            {
                GameObject gridSystemObj = new GameObject("GridSystem");
                gridSystemObj.AddComponent<GridSystem>();
                Debug.Log("✓ GridSystem 创建完成");
            }

            if (FindObjectOfType<GameTester>() == null)
            {
                GameObject testerObj = new GameObject("GameTester");
                testerObj.AddComponent<GameTester>();
                Debug.Log("✓ GameTester 创建完成");
            }
        }

        private void Update()
        {
            // 简单的测试按键
            if (Input.GetKeyDown(KeyCode.T))
            {
                Debug.Log("=== 运行基础测试 ===");
                
                if (GameManager.Instance != null)
                {
                    Debug.Log("✓ GameManager 正常工作");
                }
                else
                {
                    Debug.LogError("✗ GameManager 未找到");
                }

                GridSystem gridSystem = FindObjectOfType<GridSystem>();
                if (gridSystem != null)
                {
                    Debug.Log("✓ GridSystem 正常工作");
                }
                else
                {
                    Debug.LogError("✗ GridSystem 未找到");
                }

                Debug.Log("=== 基础测试完成 ===");
            }

            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                Debug.Log("创建简单测试...");
                CreateSimpleTest();
            }
        }

        private void CreateSimpleTest()
        {
            // 创建一个简单的测试立方体
            GameObject testCube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            testCube.name = "TestCube";
            testCube.transform.position = new Vector3(0, 1, 0);
            
            // 给立方体添加颜色
            Renderer renderer = testCube.GetComponent<Renderer>();
            Material material = new Material(Shader.Find("Standard"));
            material.color = Color.red;
            renderer.material = material;

            Debug.Log("✓ 测试立方体创建完成");
        }

        private void OnGUI()
        {
            // 显示简单的操作说明
            GUILayout.BeginArea(new Rect(10, 10, 250, 150));
            GUILayout.BeginVertical("box");

            GUILayout.Label("最简化测试版本", new GUIStyle(GUI.skin.label) { fontStyle = FontStyle.Bold });
            GUILayout.Space(10);

            GUILayout.Label("操作说明：");
            GUILayout.Label("T - 运行基础测试");
            GUILayout.Label("1 - 创建测试立方体");

            GUILayout.Space(10);

            if (GUILayout.Button("运行测试"))
            {
                Debug.Log("按钮测试 - 正常工作！");
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
}
