using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace WastelandReclaim
{
    /// <summary>
    /// 现代化UI管理器 - 高品质游戏界面
    /// </summary>
    public class ModernUIManager : MonoBehaviour
    {
        [Header("UI面板")]
        public Canvas mainCanvas;
        public GameObject gameHUD;
        public GameObject menuPanel;
        public GameObject gameOverPanel;
        public GameObject victoryPanel;

        [Header("游戏HUD元素")]
        public Text scoreText;
        public Text comboText;
        public Text stepsText;
        public Text resourcesText;
        public Slider pollutionSlider;
        public Text levelText;

        [Header("特效")]
        public GameObject comboEffectPrefab;
        public GameObject scorePopupPrefab;
        public ParticleSystem matchParticles;

        [Header("动画设置")]
        public float uiAnimationSpeed = 0.3f;
        public AnimationCurve uiEaseCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        private int currentScore = 0;
        private int currentCombo = 0;
        private Dictionary<ResourceType, int> currentResources = new Dictionary<ResourceType, int>();

        private void Start()
        {
            InitializeUI();
            SetupEventListeners();
        }

        private void InitializeUI()
        {
            CreateMainCanvas();
            CreateGameHUD();
            CreateMenuPanel();
            InitializeResources();
        }

        private void CreateMainCanvas()
        {
            if (mainCanvas == null)
            {
                GameObject canvasObj = new GameObject("ModernUI_Canvas");
                mainCanvas = canvasObj.AddComponent<Canvas>();
                mainCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
                mainCanvas.sortingOrder = 100;

                CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                scaler.matchWidthOrHeight = 0.5f;

                canvasObj.AddComponent<GraphicRaycaster>();
            }
        }

        private void CreateGameHUD()
        {
            GameObject hudObj = new GameObject("GameHUD");
            hudObj.transform.SetParent(mainCanvas.transform, false);
            gameHUD = hudObj;

            // 创建顶部信息栏
            CreateTopBar(hudObj);

            // 创建资源显示
            CreateResourceDisplay(hudObj);

            // 创建污染度显示
            CreatePollutionDisplay(hudObj);
        }

        private void CreateTopBar(GameObject parent)
        {
            GameObject topBar = new GameObject("TopBar");
            topBar.transform.SetParent(parent.transform, false);

            RectTransform rect = topBar.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0, 1);
            rect.anchorMax = new Vector2(1, 1);
            rect.offsetMin = new Vector2(20, -80);
            rect.offsetMax = new Vector2(-20, -20);

            Image bg = topBar.AddComponent<Image>();
            bg.color = new Color(0, 0, 0, 0.7f);

            // 分数显示
            CreateTextElement(topBar, "ScoreText", "分数: 0", new Vector2(0, 0.5f), new Vector2(0.3f, 0.5f));

            // 连击显示
            CreateTextElement(topBar, "ComboText", "", new Vector2(0.35f, 0.5f), new Vector2(0.65f, 0.5f));

            // 步数显示
            CreateTextElement(topBar, "StepsText", "剩余步数: 30", new Vector2(0.7f, 0.5f), new Vector2(1f, 0.5f));
        }

        private void CreateResourceDisplay(GameObject parent)
        {
            GameObject resourcePanel = new GameObject("ResourcePanel");
            resourcePanel.transform.SetParent(parent.transform, false);

            RectTransform rect = resourcePanel.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0, 1);
            rect.anchorMax = new Vector2(0.4f, 1);
            rect.offsetMin = new Vector2(20, -160);
            rect.offsetMax = new Vector2(-10, -90);

            Image bg = resourcePanel.AddComponent<Image>();
            bg.color = new Color(0.1f, 0.3f, 0.5f, 0.8f);

            resourcesText = CreateTextElement(resourcePanel, "ResourcesText", "资源:\n金属: 0\n塑料: 0",
                new Vector2(0, 0), new Vector2(1, 1)).GetComponent<Text>();
        }

        private void CreatePollutionDisplay(GameObject parent)
        {
            GameObject pollutionPanel = new GameObject("PollutionPanel");
            pollutionPanel.transform.SetParent(parent.transform, false);

            RectTransform rect = pollutionPanel.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0.6f, 1);
            rect.anchorMax = new Vector2(1f, 1);
            rect.offsetMin = new Vector2(10, -160);
            rect.offsetMax = new Vector2(-20, -90);

            Image bg = pollutionPanel.AddComponent<Image>();
            bg.color = new Color(0.5f, 0.1f, 0.1f, 0.8f);

            // 污染度滑动条
            GameObject sliderObj = new GameObject("PollutionSlider");
            sliderObj.transform.SetParent(pollutionPanel.transform, false);

            RectTransform sliderRect = sliderObj.AddComponent<RectTransform>();
            sliderRect.anchorMin = new Vector2(0.1f, 0.3f);
            sliderRect.anchorMax = new Vector2(0.9f, 0.7f);
            sliderRect.offsetMin = Vector2.zero;
            sliderRect.offsetMax = Vector2.zero;

            pollutionSlider = sliderObj.AddComponent<Slider>();
            pollutionSlider.maxValue = 100;
            pollutionSlider.value = 100;

            // 滑动条背景
            GameObject background = new GameObject("Background");
            background.transform.SetParent(sliderObj.transform, false);
            Image bgImage = background.AddComponent<Image>();
            bgImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
            pollutionSlider.targetGraphic = bgImage;

            // 滑动条填充
            GameObject fillArea = new GameObject("Fill Area");
            fillArea.transform.SetParent(sliderObj.transform, false);
            GameObject fill = new GameObject("Fill");
            fill.transform.SetParent(fillArea.transform, false);
            Image fillImage = fill.AddComponent<Image>();
            fillImage.color = new Color(0.8f, 0.2f, 0.2f, 1f);
            pollutionSlider.fillRect = fill.GetComponent<RectTransform>();

            CreateTextElement(pollutionPanel, "PollutionLabel", "污染度", new Vector2(0, 0.8f), new Vector2(1, 1));
        }

        private GameObject CreateTextElement(GameObject parent, string name, string text, Vector2 anchorMin, Vector2 anchorMax)
        {
            GameObject textObj = new GameObject(name);
            textObj.transform.SetParent(parent.transform, false);

            RectTransform rect = textObj.AddComponent<RectTransform>();
            rect.anchorMin = anchorMin;
            rect.anchorMax = anchorMax;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            Text textComponent = textObj.AddComponent<Text>();
            textComponent.text = text;
            textComponent.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            textComponent.fontSize = 16;
            textComponent.color = Color.white;
            textComponent.alignment = TextAnchor.MiddleCenter;

            return textObj;
        }

        private void CreateMenuPanel()
        {
            GameObject menuObj = new GameObject("MenuPanel");
            menuObj.transform.SetParent(mainCanvas.transform, false);
            menuPanel = menuObj;

            RectTransform rect = menuObj.AddComponent<RectTransform>();
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            Image bg = menuObj.AddComponent<Image>();
            bg.color = new Color(0, 0, 0, 0.8f);

            // 创建标题
            CreateTextElement(menuObj, "Title", "废土再生：回收者大作战", new Vector2(0.2f, 0.7f), new Vector2(0.8f, 0.9f));

            // 创建开始按钮
            CreateButton(menuObj, "StartButton", "开始游戏", new Vector2(0.4f, 0.4f), new Vector2(0.6f, 0.5f), StartGame);
        }

        private void CreateButton(GameObject parent, string name, string text, Vector2 anchorMin, Vector2 anchorMax, System.Action onClick)
        {
            GameObject buttonObj = new GameObject(name);
            buttonObj.transform.SetParent(parent.transform, false);

            RectTransform rect = buttonObj.AddComponent<RectTransform>();
            rect.anchorMin = anchorMin;
            rect.anchorMax = anchorMax;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            Image bg = buttonObj.AddComponent<Image>();
            bg.color = new Color(0.2f, 0.6f, 1f, 0.8f);

            Button button = buttonObj.AddComponent<Button>();
            button.targetGraphic = bg;
            button.onClick.AddListener(() => onClick?.Invoke());

            CreateTextElement(buttonObj, "Text", text, Vector2.zero, Vector2.one);
        }

        private void InitializeResources()
        {
            foreach (ResourceType type in System.Enum.GetValues(typeof(ResourceType)))
            {
                currentResources[type] = 0;
            }
        }

        private void SetupEventListeners()
        {
            EventSystem.Subscribe<MatchEvent>(OnMatch);
            EventSystem.Subscribe<ScoreEvent>(OnScoreUpdate);
            EventSystem.Subscribe<ResourceGainEvent>(OnResourceGain);
        }

        private void OnMatch(MatchEvent matchEvent)
        {
            currentCombo++;
            UpdateComboDisplay();
            ShowMatchEffect(matchEvent.position);
        }

        private void OnScoreUpdate(ScoreEvent scoreEvent)
        {
            currentScore += scoreEvent.points;
            UpdateScoreDisplay();
            ShowScorePopup(scoreEvent.points, scoreEvent.position);
        }

        private void OnResourceGain(ResourceGainEvent resourceEvent)
        {
            foreach (var resource in resourceEvent.resources)
            {
                currentResources[resource.Key] += resource.Value;
            }
            UpdateResourceDisplay();
        }

        private void UpdateScoreDisplay()
        {
            if (scoreText != null)
                scoreText.text = $"分数: {currentScore:N0}";
        }

        private void UpdateComboDisplay()
        {
            if (comboText != null)
            {
                if (currentCombo > 1)
                {
                    comboText.text = $"连击 x{currentCombo}!";
                    StartCoroutine(AnimateCombo());
                }
                else
                {
                    comboText.text = "";
                }
            }
        }

        private void UpdateResourceDisplay()
        {
            if (resourcesText != null)
            {
                string resourceStr = "资源:\n";
                resourceStr += $"金属: {currentResources[ResourceType.Metal]}\n";
                resourceStr += $"塑料: {currentResources[ResourceType.Plastic]}\n";
                resourceStr += $"核心: {currentResources[ResourceType.Core]}";
                resourcesText.text = resourceStr;
            }
        }

        private IEnumerator AnimateCombo()
        {
            if (comboText == null) yield break;

            Vector3 originalScale = comboText.transform.localScale;

            // 放大动画
            float duration = 0.2f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float scale = Mathf.Lerp(1f, 1.5f, elapsed / duration);
                comboText.transform.localScale = originalScale * scale;
                yield return null;
            }

            // 缩回动画
            elapsed = 0f;
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float scale = Mathf.Lerp(1.5f, 1f, elapsed / duration);
                comboText.transform.localScale = originalScale * scale;
                yield return null;
            }

            comboText.transform.localScale = originalScale;
        }

        private void ShowMatchEffect(Vector3 worldPosition)
        {
            // 创建粒子特效
            if (matchParticles != null)
            {
                matchParticles.transform.position = worldPosition;
                matchParticles.Play();
            }
        }

        private void ShowScorePopup(int points, Vector3 worldPosition)
        {
            StartCoroutine(CreateScorePopup(points, worldPosition));
        }

        private IEnumerator CreateScorePopup(int points, Vector3 worldPosition)
        {
            GameObject popup = new GameObject("ScorePopup");
            popup.transform.SetParent(mainCanvas.transform, false);

            Text text = popup.AddComponent<Text>();
            text.text = $"+{points}";
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            text.fontSize = 24;
            text.color = Color.yellow;
            text.alignment = TextAnchor.MiddleCenter;

            // 转换世界坐标到屏幕坐标
            Vector3 screenPos = Camera.main.WorldToScreenPoint(worldPosition);
            RectTransform rect = popup.GetComponent<RectTransform>();
            rect.position = screenPos;

            // 动画效果
            Vector3 startPos = rect.position;
            Vector3 endPos = startPos + Vector3.up * 100;
            Color startColor = text.color;
            Color endColor = new Color(startColor.r, startColor.g, startColor.b, 0);

            float duration = 1f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;

                rect.position = Vector3.Lerp(startPos, endPos, t);
                text.color = Color.Lerp(startColor, endColor, t);

                yield return null;
            }

            Destroy(popup);
        }

        public void StartGame()
        {
            menuPanel.SetActive(false);
            gameHUD.SetActive(true);

            // 触发游戏开始事件
            EventSystem.Publish(new GameStateChangeEvent { newState = GameState.Playing });
        }

        public void UpdatePollution(float currentPollution, float maxPollution)
        {
            if (pollutionSlider != null)
            {
                pollutionSlider.value = (currentPollution / maxPollution) * 100;

                // 根据污染度改变颜色
                Image fillImage = pollutionSlider.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    float ratio = currentPollution / maxPollution;
                    if (ratio > 0.7f)
                        fillImage.color = Color.red;
                    else if (ratio > 0.4f)
                        fillImage.color = Color.yellow;
                    else
                        fillImage.color = Color.green;
                }
            }
        }

        public void ResetCombo()
        {
            currentCombo = 0;
            UpdateComboDisplay();
        }

        private void OnDestroy()
        {
            EventSystem.Unsubscribe<MatchEvent>(OnMatch);
            EventSystem.Unsubscribe<ScoreEvent>(OnScoreUpdate);
            EventSystem.Unsubscribe<ResourceGainEvent>(OnResourceGain);
        }
    }
}