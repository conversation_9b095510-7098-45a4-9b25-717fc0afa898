using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace WastelandReclaim
{
    public enum GameState
    {
        MainMenu,
        Playing,
        Paused,
        GameOver,
        Victory,
        BaseBuilding
    }

    public class GameManager : MonoBehaviour
    {
        public static GameManager Instance { get; private set; }

        [Header("Game State")]
        public GameState currentState = GameState.MainMenu;
        public LevelData currentLevel;
        public PlayerData playerData;

        [Header("Systems")]
        public GridSystem gridSystem;
        public MatchSystem matchSystem;
        public TowerSystem towerSystem;
        public EnemySystem enemySystem;
        public UIManager uiManager;
        public AudioManager audioManager;

        [Header("Game Settings")]
        public int maxSteps = 50;
        public float timeLimit = 300f;
        public int targetPollution = 10;

        // Current game session data
        private int currentSteps = 0;
        private float currentTime = 0f;
        private int currentPollution = 100;
        private Dictionary<ResourceType, int> sessionResources;

        // Events
        public System.Action<GameState> OnGameStateChanged;
        public System.Action<int> OnStepsChanged;
        public System.Action<float> OnTimeChanged;
        public System.Action<int> OnPollutionChanged;
        public System.Action<Dictionary<ResourceType, int>> OnResourcesChanged;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeSystems();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            LoadPlayerData();
            ChangeGameState(GameState.MainMenu);
        }

        private void Update()
        {
            if (currentState == GameState.Playing)
            {
                UpdateGameTime();
                CheckWinConditions();
                CheckLoseConditions();
            }
        }

        private void InitializeSystems()
        {
            // Initialize all game systems
            if (gridSystem != null) gridSystem.Initialize();
            if (matchSystem != null) matchSystem.Initialize();
            if (towerSystem != null) towerSystem.Initialize();
            if (enemySystem != null) enemySystem.Initialize();
            if (uiManager != null) uiManager.Initialize();
            if (audioManager != null) audioManager.Initialize();

            // Initialize session resources
            sessionResources = new Dictionary<ResourceType, int>();
            foreach (ResourceType type in System.Enum.GetValues(typeof(ResourceType)))
            {
                sessionResources[type] = 0;
            }

            // Subscribe to events
            EventSystem.Subscribe<MatchEvent>(OnMatchCompleted);
            EventSystem.Subscribe<EnemyReachedEvent>(OnEnemyReached);
            EventSystem.Subscribe<TowerBuiltEvent>(OnTowerBuilt);
        }

        public void ChangeGameState(GameState newState)
        {
            GameState previousState = currentState;
            currentState = newState;

            Debug.Log($"Game state changed from {previousState} to {newState}");

            // Handle state transitions
            switch (newState)
            {
                case GameState.MainMenu:
                    HandleMainMenuState();
                    break;
                case GameState.Playing:
                    HandlePlayingState();
                    break;
                case GameState.Paused:
                    HandlePausedState();
                    break;
                case GameState.GameOver:
                    HandleGameOverState();
                    break;
                case GameState.Victory:
                    HandleVictoryState();
                    break;
                case GameState.BaseBuilding:
                    HandleBaseBuildingState();
                    break;
            }

            OnGameStateChanged?.Invoke(newState);
        }

        public void StartLevel(LevelData levelData)
        {
            currentLevel = levelData;
            
            // Reset game session
            currentSteps = 0;
            currentTime = 0f;
            currentPollution = levelData.initialPollution;
            maxSteps = levelData.stepLimit;
            timeLimit = levelData.timeLimit;
            targetPollution = levelData.targetPollution;

            // Reset session resources
            foreach (ResourceType type in System.Enum.GetValues(typeof(ResourceType)))
            {
                sessionResources[type] = 0;
            }

            // Load level into systems
            gridSystem.LoadLevel(levelData);
            enemySystem.SetupWaves(levelData.enemyWaves);
            towerSystem.ClearTowers();

            ChangeGameState(GameState.Playing);

            // Notify UI
            OnStepsChanged?.Invoke(currentSteps);
            OnTimeChanged?.Invoke(currentTime);
            OnPollutionChanged?.Invoke(currentPollution);
            OnResourcesChanged?.Invoke(sessionResources);

            EventSystem.Publish(new LevelStartEvent(levelData));
        }

        public bool TryUseStep()
        {
            if (currentSteps >= maxSteps)
            {
                return false;
            }

            currentSteps++;
            OnStepsChanged?.Invoke(currentSteps);
            return true;
        }

        public void AddResources(Dictionary<ResourceType, int> resources)
        {
            foreach (var kvp in resources)
            {
                sessionResources[kvp.Key] += kvp.Value;
            }
            OnResourcesChanged?.Invoke(sessionResources);
        }

        public bool CanAfford(Dictionary<ResourceType, int> cost)
        {
            foreach (var kvp in cost)
            {
                if (sessionResources[kvp.Key] < kvp.Value)
                {
                    return false;
                }
            }
            return true;
        }

        public void SpendResources(Dictionary<ResourceType, int> cost)
        {
            foreach (var kvp in cost)
            {
                sessionResources[kvp.Key] -= kvp.Value;
            }
            OnResourcesChanged?.Invoke(sessionResources);
        }

        public void ReducePollution(int amount)
        {
            currentPollution = Mathf.Max(0, currentPollution - amount);
            OnPollutionChanged?.Invoke(currentPollution);
        }

        public void IncreasePollution(int amount)
        {
            currentPollution += amount;
            OnPollutionChanged?.Invoke(currentPollution);
        }

        private void UpdateGameTime()
        {
            if (timeLimit > 0)
            {
                currentTime += Time.deltaTime;
                OnTimeChanged?.Invoke(currentTime);
            }
        }

        private void CheckWinConditions()
        {
            if (currentPollution <= targetPollution)
            {
                ChangeGameState(GameState.Victory);
            }
        }

        private void CheckLoseConditions()
        {
            if (currentSteps >= maxSteps && currentPollution > targetPollution)
            {
                ChangeGameState(GameState.GameOver);
            }

            if (timeLimit > 0 && currentTime >= timeLimit && currentPollution > targetPollution)
            {
                ChangeGameState(GameState.GameOver);
            }
        }

        private void HandleMainMenuState()
        {
            Time.timeScale = 1f;
            if (uiManager != null)
            {
                uiManager.ShowMainMenu();
            }
        }

        private void HandlePlayingState()
        {
            Time.timeScale = 1f;
            if (uiManager != null)
            {
                uiManager.ShowGameUI();
            }
        }

        private void HandlePausedState()
        {
            Time.timeScale = 0f;
            if (uiManager != null)
            {
                uiManager.ShowPauseMenu();
            }
        }

        private void HandleGameOverState()
        {
            Time.timeScale = 0f;
            if (uiManager != null)
            {
                uiManager.ShowGameOverScreen();
            }
            EventSystem.Publish(new GameOverEvent());
        }

        private void HandleVictoryState()
        {
            Time.timeScale = 0f;
            CalculateRewards();
            if (uiManager != null)
            {
                uiManager.ShowVictoryScreen();
            }
            EventSystem.Publish(new LevelCompleteEvent(currentLevel));
        }

        private void HandleBaseBuildingState()
        {
            Time.timeScale = 1f;
            if (uiManager != null)
            {
                uiManager.ShowBaseBuilding();
            }
        }

        private void CalculateRewards()
        {
            // Calculate level completion rewards
            var rewards = new Dictionary<ResourceType, int>();
            
            // Base rewards
            foreach (var reward in currentLevel.basicReward)
            {
                rewards[reward.Key] = reward.Value;
            }

            // Bonus for efficiency
            if (currentSteps < maxSteps * 0.8f)
            {
                foreach (var reward in rewards.Keys.ToList())
                {
                    rewards[reward] = Mathf.RoundToInt(rewards[reward] * 1.2f);
                }
            }

            // Add rewards to player data
            foreach (var reward in rewards)
            {
                playerData.AddResource(reward.Key, reward.Value);
            }

            SavePlayerData();
        }

        private void OnMatchCompleted(MatchEvent matchEvent)
        {
            // Add resources from match
            AddResources(matchEvent.resources);
            
            // Reduce pollution
            int pollutionReduction = matchEvent.matchedBlocks.Count * 2;
            if (matchEvent.combo > 1)
            {
                pollutionReduction = Mathf.RoundToInt(pollutionReduction * (1 + matchEvent.combo * 0.1f));
            }
            ReducePollution(pollutionReduction);
        }

        private void OnEnemyReached(EnemyReachedEvent enemyEvent)
        {
            IncreasePollution(enemyEvent.pollutionDamage);
        }

        private void OnTowerBuilt(TowerBuiltEvent towerEvent)
        {
            SpendResources(towerEvent.cost);
        }

        private void LoadPlayerData()
        {
            playerData = SaveSystem.LoadPlayerData();
            if (playerData == null)
            {
                playerData = new PlayerData();
                SavePlayerData();
            }
        }

        private void SavePlayerData()
        {
            SaveSystem.SavePlayerData(playerData);
        }

        public void RestartLevel()
        {
            if (currentLevel != null)
            {
                StartLevel(currentLevel);
            }
        }

        public void ReturnToMainMenu()
        {
            ChangeGameState(GameState.MainMenu);
        }

        public void PauseGame()
        {
            if (currentState == GameState.Playing)
            {
                ChangeGameState(GameState.Paused);
            }
        }

        public void ResumeGame()
        {
            if (currentState == GameState.Paused)
            {
                ChangeGameState(GameState.Playing);
            }
        }

        public void GoToBaseBuilding()
        {
            ChangeGameState(GameState.BaseBuilding);
        }

        private void OnDestroy()
        {
            EventSystem.Unsubscribe<MatchEvent>(OnMatchCompleted);
            EventSystem.Unsubscribe<EnemyReachedEvent>(OnEnemyReached);
            EventSystem.Unsubscribe<TowerBuiltEvent>(OnTowerBuilt);
        }
    }
}
