# 《废土再生：回收者大作战》技术架构设计文档

## 1. 总体架构设计

### 1.1 架构概览

```
┌─────────────────────────────────────────────────────────┐
│                    客户端层 (Unity)                     │
├─────────────────────────────────────────────────────────┤
│                    网络通信层 (HTTP/WebSocket)           │
├─────────────────────────────────────────────────────────┤
│                    API网关层 (Nginx/Kong)               │
├─────────────────────────────────────────────────────────┤
│  用户服务  │  游戏服务  │  社交服务  │  支付服务  │  数据服务  │
├─────────────────────────────────────────────────────────┤
│              数据存储层 (MongoDB/Redis/MySQL)            │
├─────────────────────────────────────────────────────────┤
│                 基础设施层 (AWS/阿里云)                  │
└─────────────────────────────────────────────────────────┘
```

### 1.2 技术栈选择

**客户端技术栈**:
- **游戏引擎**: Unity 2022.3 LTS
- **编程语言**: C# (.NET Standard 2.1)
- **UI框架**: Unity UI Toolkit (UI Builder)
- **动画系统**: Unity Timeline + Cinemachine
- **音频**: Unity Audio + Wwise (可选)

**服务端技术栈**:
- **运行环境**: Node.js 18+ / Docker
- **Web框架**: Express.js + TypeScript
- **数据库**: MongoDB (主数据) + Redis (缓存) + MySQL (分析)
- **消息队列**: Redis Pub/Sub / RabbitMQ
- **文件存储**: AWS S3 / 阿里云OSS

**DevOps工具链**:
- **版本控制**: Git + GitLab/GitHub
- **CI/CD**: GitLab CI / GitHub Actions
- **容器化**: Docker + Kubernetes
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)

## 2. 客户端架构设计

### 2.1 模块化架构

```
GameClient/
├── Core/                    # 核心系统
│   ├── GameManager.cs       # 游戏总控制器
│   ├── SceneManager.cs      # 场景管理
│   ├── EventSystem.cs       # 事件系统
│   └── ServiceLocator.cs    # 服务定位器
├── Gameplay/                # 游戏玩法
│   ├── Grid/                # 网格系统
│   ├── Match/               # 消除系统
│   ├── Tower/               # 塔防系统
│   └── Enemy/               # 敌人系统
├── UI/                      # 用户界面
│   ├── Screens/             # 界面屏幕
│   ├── Components/          # UI组件
│   └── Animations/          # UI动画
├── Data/                    # 数据管理
│   ├── Models/              # 数据模型
│   ├── Persistence/         # 持久化
│   └── Network/             # 网络通信
├── Audio/                   # 音频系统
├── Graphics/                # 图形渲染
└── Utils/                   # 工具类
```

### 2.2 核心系统设计

#### 2.2.1 游戏管理器 (GameManager)

```csharp
public class GameManager : MonoBehaviour
{
    public static GameManager Instance { get; private set; }
    
    [Header("Game State")]
    public GameState currentState;
    public LevelData currentLevel;
    
    [Header("Systems")]
    public GridSystem gridSystem;
    public MatchSystem matchSystem;
    public TowerSystem towerSystem;
    public EnemySystem enemySystem;
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeSystems();
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void InitializeSystems()
    {
        // 初始化各个子系统
        gridSystem.Initialize();
        matchSystem.Initialize();
        towerSystem.Initialize();
        enemySystem.Initialize();
        
        // 注册事件监听
        EventSystem.Subscribe<LevelCompleteEvent>(OnLevelComplete);
        EventSystem.Subscribe<GameOverEvent>(OnGameOver);
    }
    
    public void StartLevel(LevelData levelData)
    {
        currentLevel = levelData;
        currentState = GameState.Playing;
        
        gridSystem.LoadLevel(levelData);
        enemySystem.SetupWaves(levelData.enemyWaves);
        
        EventSystem.Publish(new LevelStartEvent(levelData));
    }
}
```

#### 2.2.2 网格系统 (GridSystem)

```csharp
public class GridSystem : MonoBehaviour
{
    [Header("Grid Configuration")]
    public int gridWidth = 8;
    public int gridHeight = 8;
    public float cellSize = 1.0f;
    
    private WasteBlock[,] grid;
    private List<Vector2Int> selectedBlocks = new List<Vector2Int>();
    
    public void Initialize()
    {
        grid = new WasteBlock[gridWidth, gridHeight];
        CreateGridVisual();
    }
    
    public void LoadLevel(LevelData levelData)
    {
        gridWidth = levelData.gridSize.x;
        gridHeight = levelData.gridSize.y;
        
        // 清空现有网格
        ClearGrid();
        
        // 根据关卡数据生成废料块
        foreach (var wasteData in levelData.wasteBlocks)
        {
            CreateWasteBlock(wasteData);
        }
    }
    
    public bool TryMatch(Vector2Int position)
    {
        var matchedBlocks = FindMatches(position);
        if (matchedBlocks.Count >= 3)
        {
            RemoveBlocks(matchedBlocks);
            ApplyGravity();
            return true;
        }
        return false;
    }
    
    private List<Vector2Int> FindMatches(Vector2Int startPos)
    {
        var matches = new List<Vector2Int>();
        var blockType = grid[startPos.x, startPos.y].blockType;
        
        // 使用BFS查找连续的同类型块
        var queue = new Queue<Vector2Int>();
        var visited = new HashSet<Vector2Int>();
        
        queue.Enqueue(startPos);
        visited.Add(startPos);
        
        while (queue.Count > 0)
        {
            var current = queue.Dequeue();
            matches.Add(current);
            
            // 检查四个方向的相邻块
            foreach (var direction in GetDirections())
            {
                var neighbor = current + direction;
                if (IsValidPosition(neighbor) && 
                    !visited.Contains(neighbor) &&
                    grid[neighbor.x, neighbor.y]?.blockType == blockType)
                {
                    queue.Enqueue(neighbor);
                    visited.Add(neighbor);
                }
            }
        }
        
        return matches;
    }
}
```

#### 2.2.3 消除系统 (MatchSystem)

```csharp
public class MatchSystem : MonoBehaviour
{
    [Header("Match Configuration")]
    public int minMatchCount = 3;
    public float matchDelay = 0.5f;
    public ParticleSystem matchEffect;
    
    private int currentCombo = 0;
    private float comboTimer = 0f;
    private const float comboTimeLimit = 2f;
    
    public void ProcessMatch(List<Vector2Int> matchedBlocks)
    {
        if (matchedBlocks.Count < minMatchCount) return;
        
        // 计算奖励倍数
        float multiplier = CalculateMultiplier(matchedBlocks.Count);
        
        // 更新连击数
        UpdateCombo();
        
        // 播放特效
        PlayMatchEffect(matchedBlocks);
        
        // 计算资源奖励
        var resources = CalculateResources(matchedBlocks, multiplier);
        
        // 发布消除事件
        EventSystem.Publish(new MatchEvent
        {
            matchedBlocks = matchedBlocks,
            resources = resources,
            combo = currentCombo
        });
        
        // 检查特殊效果
        CheckSpecialEffects(matchedBlocks);
    }
    
    private float CalculateMultiplier(int matchCount)
    {
        float baseMultiplier = 1f;
        
        // 连消奖励
        if (matchCount >= 4) baseMultiplier *= 1.5f;
        if (matchCount >= 5) baseMultiplier *= 2f;
        if (matchCount >= 6) baseMultiplier *= 3f;
        
        // 连击奖励
        baseMultiplier *= (1f + currentCombo * 0.1f);
        
        return baseMultiplier;
    }
    
    private void CheckSpecialEffects(List<Vector2Int> matchedBlocks)
    {
        // 检查是否形成特殊形状
        if (IsLShape(matchedBlocks))
        {
            TriggerLShapeEffect(matchedBlocks);
        }
        else if (IsTShape(matchedBlocks))
        {
            TriggerTShapeEffect(matchedBlocks);
        }
        
        // 检查元素反应
        CheckElementalReactions(matchedBlocks);
    }
}
```

### 2.3 数据管理系统

#### 2.3.1 数据模型定义

```csharp
[System.Serializable]
public class LevelData
{
    public int levelId;
    public string levelName;
    public int difficulty;
    public Vector2Int gridSize;
    public int initialPollution;
    public int targetPollution;
    public int stepLimit;
    public float timeLimit;
    public List<WasteBlockData> wasteBlocks;
    public List<EnemyWaveData> enemyWaves;
    public LevelRewards rewards;
}

[System.Serializable]
public class WasteBlockData
{
    public WasteBlockType type;
    public Vector2Int position;
    public int pollutionValue;
    public Dictionary<string, object> properties;
}

[System.Serializable]
public class PlayerData
{
    public string playerId;
    public int level;
    public int experience;
    public Dictionary<ResourceType, int> resources;
    public List<int> completedLevels;
    public BaseData baseData;
    public SettingsData settings;
}
```

#### 2.3.2 存档系统

```csharp
public class SaveSystem : MonoBehaviour
{
    private const string SAVE_KEY = "PlayerSave";
    private const string CLOUD_SAVE_ENDPOINT = "/api/save";
    
    public static void SaveGame(PlayerData playerData)
    {
        try
        {
            // 本地存储
            string jsonData = JsonUtility.ToJson(playerData);
            string encryptedData = EncryptionHelper.Encrypt(jsonData);
            PlayerPrefs.SetString(SAVE_KEY, encryptedData);
            PlayerPrefs.Save();
            
            // 云端备份
            if (NetworkManager.IsConnected)
            {
                StartCoroutine(UploadSaveData(playerData));
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Save failed: {e.Message}");
        }
    }
    
    public static PlayerData LoadGame()
    {
        try
        {
            // 尝试从云端加载
            if (NetworkManager.IsConnected)
            {
                var cloudData = DownloadSaveData();
                if (cloudData != null) return cloudData;
            }
            
            // 从本地加载
            if (PlayerPrefs.HasKey(SAVE_KEY))
            {
                string encryptedData = PlayerPrefs.GetString(SAVE_KEY);
                string jsonData = EncryptionHelper.Decrypt(encryptedData);
                return JsonUtility.FromJson<PlayerData>(jsonData);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Load failed: {e.Message}");
        }
        
        // 返回默认数据
        return CreateDefaultPlayerData();
    }
}
```

## 3. 服务端架构设计

### 3.1 微服务架构

```
services/
├── user-service/           # 用户服务
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── routes/         # 路由定义
│   ├── Dockerfile
│   └── package.json
├── game-service/           # 游戏服务
├── social-service/         # 社交服务
├── payment-service/        # 支付服务
├── analytics-service/      # 数据分析服务
└── gateway/                # API网关
```

### 3.2 用户服务设计

```typescript
// user-service/src/controllers/UserController.ts
import { Request, Response } from 'express';
import { UserService } from '../services/UserService';
import { AuthMiddleware } from '../middleware/AuthMiddleware';

export class UserController {
    private userService: UserService;
    
    constructor() {
        this.userService = new UserService();
    }
    
    @AuthMiddleware.requireAuth
    async getProfile(req: Request, res: Response) {
        try {
            const userId = req.user.id;
            const profile = await this.userService.getProfile(userId);
            res.json({ success: true, data: profile });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    }
    
    @AuthMiddleware.requireAuth
    async updateProfile(req: Request, res: Response) {
        try {
            const userId = req.user.id;
            const updateData = req.body;
            const result = await this.userService.updateProfile(userId, updateData);
            res.json({ success: true, data: result });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    }
}
```

### 3.3 游戏服务设计

```typescript
// game-service/src/services/GameService.ts
import { LevelData, GameSession, PlayerProgress } from '../models';
import { RedisClient } from '../utils/RedisClient';
import { ValidationService } from './ValidationService';

export class GameService {
    private redis: RedisClient;
    private validator: ValidationService;
    
    constructor() {
        this.redis = new RedisClient();
        this.validator = new ValidationService();
    }
    
    async startLevel(userId: string, levelId: number): Promise<GameSession> {
        // 验证用户是否有权限访问该关卡
        const hasAccess = await this.validator.validateLevelAccess(userId, levelId);
        if (!hasAccess) {
            throw new Error('Level not accessible');
        }
        
        // 创建游戏会话
        const session: GameSession = {
            sessionId: generateUUID(),
            userId,
            levelId,
            startTime: Date.now(),
            moves: [],
            resources: {},
            status: 'active'
        };
        
        // 缓存会话数据
        await this.redis.setex(`session:${session.sessionId}`, 3600, JSON.stringify(session));
        
        return session;
    }
    
    async submitMove(sessionId: string, moveData: any): Promise<MoveResult> {
        // 获取会话数据
        const sessionData = await this.redis.get(`session:${sessionId}`);
        if (!sessionData) {
            throw new Error('Session not found or expired');
        }
        
        const session: GameSession = JSON.parse(sessionData);
        
        // 验证移动的合法性
        const isValid = await this.validator.validateMove(session, moveData);
        if (!isValid) {
            throw new Error('Invalid move');
        }
        
        // 处理移动逻辑
        const result = await this.processMove(session, moveData);
        
        // 更新会话数据
        session.moves.push(moveData);
        await this.redis.setex(`session:${sessionId}`, 3600, JSON.stringify(session));
        
        return result;
    }
    
    async completeLevel(sessionId: string, completionData: any): Promise<LevelResult> {
        const sessionData = await this.redis.get(`session:${sessionId}`);
        if (!sessionData) {
            throw new Error('Session not found');
        }
        
        const session: GameSession = JSON.parse(sessionData);
        
        // 验证完成数据
        const isValid = await this.validator.validateCompletion(session, completionData);
        if (!isValid) {
            throw new Error('Invalid completion data');
        }
        
        // 计算奖励
        const rewards = await this.calculateRewards(session, completionData);
        
        // 更新玩家进度
        await this.updatePlayerProgress(session.userId, session.levelId, rewards);
        
        // 清理会话数据
        await this.redis.del(`session:${sessionId}`);
        
        return {
            success: true,
            rewards,
            nextLevelUnlocked: await this.checkNextLevelUnlock(session.userId, session.levelId)
        };
    }
}
```

## 4. 数据库设计

### 4.1 MongoDB 集合设计

```javascript
// users 集合
{
  _id: ObjectId,
  userId: String,
  username: String,
  email: String,
  passwordHash: String,
  profile: {
    level: Number,
    experience: Number,
    avatar: String,
    createdAt: Date,
    lastLoginAt: Date
  },
  gameData: {
    resources: {
      metal: Number,
      plastic: Number,
      electronic: Number,
      glass: Number,
      organic: Number,
      cores: Number
    },
    completedLevels: [Number],
    baseData: {
      buildings: [{
        type: String,
        level: Number,
        position: {x: Number, y: Number}
      }],
      technologies: [String]
    }
  },
  settings: {
    soundEnabled: Boolean,
    musicEnabled: Boolean,
    language: String,
    notifications: Boolean
  }
}

// levels 集合
{
  _id: ObjectId,
  levelId: Number,
  chapterName: String,
  levelName: String,
  difficulty: Number,
  gridSize: {width: Number, height: Number},
  initialPollution: Number,
  targetPollution: Number,
  stepLimit: Number,
  timeLimit: Number,
  wasteBlocks: [{
    type: String,
    position: {x: Number, y: Number},
    properties: Object
  }],
  enemies: [{
    type: String,
    spawnTime: Number,
    path: [Object],
    properties: Object
  }],
  rewards: {
    basicReward: Object,
    starRewards: [Object]
  }
}

// game_sessions 集合
{
  _id: ObjectId,
  sessionId: String,
  userId: String,
  levelId: Number,
  startTime: Date,
  endTime: Date,
  moves: [Object],
  currentState: Object,
  status: String // 'active', 'completed', 'abandoned'
}
```

### 4.2 Redis 缓存策略

```javascript
// 缓存键命名规范
const CACHE_KEYS = {
  USER_PROFILE: 'user:profile:{userId}',
  LEVEL_DATA: 'level:data:{levelId}',
  GAME_SESSION: 'session:{sessionId}',
  LEADERBOARD: 'leaderboard:{type}:{period}',
  DAILY_CHALLENGES: 'challenges:daily:{date}'
};

// 缓存过期时间
const CACHE_TTL = {
  USER_PROFILE: 3600,      // 1小时
  LEVEL_DATA: 86400,       // 24小时
  GAME_SESSION: 3600,      // 1小时
  LEADERBOARD: 300,        // 5分钟
  DAILY_CHALLENGES: 86400  // 24小时
};
```

## 5. 性能优化策略

### 5.1 客户端优化

**内存管理**:
```csharp
public class ObjectPool<T> where T : MonoBehaviour
{
    private Queue<T> pool = new Queue<T>();
    private T prefab;
    private Transform parent;
    
    public ObjectPool(T prefab, int initialSize, Transform parent = null)
    {
        this.prefab = prefab;
        this.parent = parent;
        
        for (int i = 0; i < initialSize; i++)
        {
            var obj = Object.Instantiate(prefab, parent);
            obj.gameObject.SetActive(false);
            pool.Enqueue(obj);
        }
    }
    
    public T Get()
    {
        if (pool.Count > 0)
        {
            var obj = pool.Dequeue();
            obj.gameObject.SetActive(true);
            return obj;
        }
        else
        {
            return Object.Instantiate(prefab, parent);
        }
    }
    
    public void Return(T obj)
    {
        obj.gameObject.SetActive(false);
        pool.Enqueue(obj);
    }
}
```

**渲染优化**:
- 使用Sprite Atlas减少Draw Call
- 实现LOD系统，远距离对象使用低精度模型
- 动态批处理相同材质的对象
- 使用Occlusion Culling剔除不可见对象

### 5.2 服务端优化

**数据库优化**:
```javascript
// MongoDB 索引策略
db.users.createIndex({ "userId": 1 }, { unique: true });
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "profile.level": -1 });

db.levels.createIndex({ "levelId": 1 }, { unique: true });
db.levels.createIndex({ "difficulty": 1 });

db.game_sessions.createIndex({ "sessionId": 1 }, { unique: true });
db.game_sessions.createIndex({ "userId": 1, "startTime": -1 });
db.game_sessions.createIndex({ "status": 1 });

// 复合索引
db.users.createIndex({ "gameData.completedLevels": 1, "profile.level": -1 });
```

**API优化**:
```typescript
// 请求限流
import rateLimit from 'express-rate-limit';

const gameApiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: 'Too many requests from this IP'
});

// 响应压缩
import compression from 'compression';
app.use(compression());

// 缓存中间件
const cacheMiddleware = (ttl: number) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const key = `cache:${req.originalUrl}`;
    const cached = await redis.get(key);
    
    if (cached) {
      return res.json(JSON.parse(cached));
    }
    
    // 重写res.json以缓存响应
    const originalJson = res.json;
    res.json = function(data) {
      redis.setex(key, ttl, JSON.stringify(data));
      return originalJson.call(this, data);
    };
    
    next();
  };
};
```

## 6. 安全设计

### 6.1 客户端安全

**代码混淆**:
```csharp
// 使用IL2CPP编译，增加逆向难度
// 关键数据加密存储
public static class SecurityHelper
{
    private static readonly string ENCRYPTION_KEY = "YourSecretKey";
    
    public static string Encrypt(string plainText)
    {
        // 使用AES加密
        using (Aes aes = Aes.Create())
        {
            aes.Key = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);
            aes.IV = new byte[16]; // 简化示例，实际应使用随机IV
            
            using (var encryptor = aes.CreateEncryptor())
            using (var ms = new MemoryStream())
            using (var cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
            using (var writer = new StreamWriter(cs))
            {
                writer.Write(plainText);
                return Convert.ToBase64String(ms.ToArray());
            }
        }
    }
}
```

### 6.2 服务端安全

**身份验证**:
```typescript
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';

export class AuthService {
    private static readonly JWT_SECRET = process.env.JWT_SECRET!;
    private static readonly JWT_EXPIRES_IN = '7d';
    
    static async hashPassword(password: string): Promise<string> {
        const saltRounds = 12;
        return bcrypt.hash(password, saltRounds);
    }
    
    static async verifyPassword(password: string, hash: string): Promise<boolean> {
        return bcrypt.compare(password, hash);
    }
    
    static generateToken(userId: string): string {
        return jwt.sign({ userId }, this.JWT_SECRET, {
            expiresIn: this.JWT_EXPIRES_IN
        });
    }
    
    static verifyToken(token: string): any {
        return jwt.verify(token, this.JWT_SECRET);
    }
}
```

**防作弊机制**:
```typescript
export class AntiCheatService {
    // 验证游戏进度的合理性
    static validateGameProgress(session: GameSession, completionData: any): boolean {
        // 检查完成时间是否合理
        const minCompletionTime = this.calculateMinTime(session.levelId);
        const actualTime = Date.now() - session.startTime;
        if (actualTime < minCompletionTime) {
            return false;
        }
        
        // 检查移动序列是否合法
        if (!this.validateMoveSequence(session.moves)) {
            return false;
        }
        
        // 检查资源获取是否合理
        if (!this.validateResourceGain(session, completionData)) {
            return false;
        }
        
        return true;
    }
    
    // 检测异常行为模式
    static detectAnomalousPattern(userId: string, recentSessions: GameSession[]): boolean {
        // 检查胜率是否异常
        const winRate = this.calculateWinRate(recentSessions);
        if (winRate > 0.95) { // 胜率过高可能存在作弊
            return true;
        }
        
        // 检查平均完成时间是否异常
        const avgTime = this.calculateAverageTime(recentSessions);
        const expectedTime = this.getExpectedTime(recentSessions);
        if (avgTime < expectedTime * 0.5) { // 完成时间过短
            return true;
        }
        
        return false;
    }
}
```

---

*技术架构设计是游戏开发的基础，合理的架构能够确保游戏的稳定性、可扩展性和安全性。通过模块化设计、性能优化和安全防护，为玩家提供流畅、安全的游戏体验。*
