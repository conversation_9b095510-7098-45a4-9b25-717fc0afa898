using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    [System.Serializable]
    public class AudioClipData
    {
        public string name;
        public AudioClip clip;
        [Range(0f, 1f)]
        public float volume = 1f;
        [Range(0.1f, 3f)]
        public float pitch = 1f;
        public bool loop = false;
    }

    public class AudioManager : MonoBehaviour
    {
        [Header("Audio Sources")]
        public AudioSource musicSource;
        public AudioSource sfxSource;
        public AudioSource ambientSource;
        public AudioSource uiSource;

        [Header("Music Clips")]
        public AudioClipData[] musicClips;

        [Header("SFX Clips")]
        public AudioClipData[] sfxClips;

        [Header("UI Clips")]
        public AudioClipData[] uiClips;

        [Header("Ambient Clips")]
        public AudioClipData[] ambientClips;

        [Header("Settings")]
        [Range(0f, 1f)]
        public float masterVolume = 1f;
        [Range(0f, 1f)]
        public float musicVolume = 0.7f;
        [Range(0f, 1f)]
        public float sfxVolume = 1f;
        [Range(0f, 1f)]
        public float uiVolume = 0.8f;
        [Range(0f, 1f)]
        public float ambientVolume = 0.5f;

        private Dictionary<string, AudioClipData> musicDict = new Dictionary<string, AudioClipData>();
        private Dictionary<string, AudioClipData> sfxDict = new Dictionary<string, AudioClipData>();
        private Dictionary<string, AudioClipData> uiDict = new Dictionary<string, AudioClipData>();
        private Dictionary<string, AudioClipData> ambientDict = new Dictionary<string, AudioClipData>();

        private string currentMusicTrack = "";
        private Coroutine musicFadeCoroutine;

        public void Initialize()
        {
            SetupAudioSources();
            BuildAudioDictionaries();
            LoadAudioSettings();
            
            // Subscribe to events
            EventSystem.Subscribe<MatchEvent>(OnMatchEvent);
            EventSystem.Subscribe<TowerBuiltEvent>(OnTowerBuilt);
            EventSystem.Subscribe<EnemyDefeatedEvent>(OnEnemyDefeated);
            EventSystem.Subscribe<LevelStartEvent>(OnLevelStart);
            EventSystem.Subscribe<LevelCompleteEvent>(OnLevelComplete);
            EventSystem.Subscribe<GameOverEvent>(OnGameOver);
        }

        private void SetupAudioSources()
        {
            if (musicSource == null)
            {
                GameObject musicObj = new GameObject("MusicSource");
                musicObj.transform.SetParent(transform);
                musicSource = musicObj.AddComponent<AudioSource>();
                musicSource.loop = true;
                musicSource.playOnAwake = false;
            }

            if (sfxSource == null)
            {
                GameObject sfxObj = new GameObject("SFXSource");
                sfxObj.transform.SetParent(transform);
                sfxSource = sfxObj.AddComponent<AudioSource>();
                sfxSource.playOnAwake = false;
            }

            if (ambientSource == null)
            {
                GameObject ambientObj = new GameObject("AmbientSource");
                ambientObj.transform.SetParent(transform);
                ambientSource = ambientObj.AddComponent<AudioSource>();
                ambientSource.loop = true;
                ambientSource.playOnAwake = false;
            }

            if (uiSource == null)
            {
                GameObject uiObj = new GameObject("UISource");
                uiObj.transform.SetParent(transform);
                uiSource = uiObj.AddComponent<AudioSource>();
                uiSource.playOnAwake = false;
            }
        }

        private void BuildAudioDictionaries()
        {
            // Build music dictionary
            foreach (var clipData in musicClips)
            {
                if (!string.IsNullOrEmpty(clipData.name) && clipData.clip != null)
                {
                    musicDict[clipData.name] = clipData;
                }
            }

            // Build SFX dictionary
            foreach (var clipData in sfxClips)
            {
                if (!string.IsNullOrEmpty(clipData.name) && clipData.clip != null)
                {
                    sfxDict[clipData.name] = clipData;
                }
            }

            // Build UI dictionary
            foreach (var clipData in uiClips)
            {
                if (!string.IsNullOrEmpty(clipData.name) && clipData.clip != null)
                {
                    uiDict[clipData.name] = clipData;
                }
            }

            // Build ambient dictionary
            foreach (var clipData in ambientClips)
            {
                if (!string.IsNullOrEmpty(clipData.name) && clipData.clip != null)
                {
                    ambientDict[clipData.name] = clipData;
                }
            }
        }

        private void LoadAudioSettings()
        {
            SettingsData settings = SaveSystem.LoadSettings();
            
            masterVolume = 1f; // Master volume is controlled separately
            musicVolume = settings.musicVolume;
            sfxVolume = settings.soundVolume;
            uiVolume = settings.soundVolume;
            ambientVolume = settings.soundVolume * 0.5f;

            UpdateAudioSourceVolumes();
        }

        private void UpdateAudioSourceVolumes()
        {
            if (musicSource != null)
                musicSource.volume = masterVolume * musicVolume;
            
            if (sfxSource != null)
                sfxSource.volume = masterVolume * sfxVolume;
            
            if (uiSource != null)
                uiSource.volume = masterVolume * uiVolume;
            
            if (ambientSource != null)
                ambientSource.volume = masterVolume * ambientVolume;
        }

        // Music Control
        public void PlayMusic(string trackName, bool fadeIn = true)
        {
            if (musicDict.ContainsKey(trackName))
            {
                if (currentMusicTrack == trackName && musicSource.isPlaying)
                    return;

                AudioClipData clipData = musicDict[trackName];
                
                if (fadeIn && musicSource.isPlaying)
                {
                    StartCoroutine(CrossfadeMusic(clipData));
                }
                else
                {
                    musicSource.clip = clipData.clip;
                    musicSource.volume = masterVolume * musicVolume * clipData.volume;
                    musicSource.pitch = clipData.pitch;
                    musicSource.loop = clipData.loop;
                    musicSource.Play();
                }

                currentMusicTrack = trackName;
            }
            else
            {
                Debug.LogWarning($"Music track '{trackName}' not found!");
            }
        }

        public void StopMusic(bool fadeOut = true)
        {
            if (fadeOut)
            {
                if (musicFadeCoroutine != null)
                    StopCoroutine(musicFadeCoroutine);
                musicFadeCoroutine = StartCoroutine(FadeOutMusic());
            }
            else
            {
                musicSource.Stop();
            }
            
            currentMusicTrack = "";
        }

        public void PauseMusic()
        {
            musicSource.Pause();
        }

        public void ResumeMusic()
        {
            musicSource.UnPause();
        }

        // SFX Control
        public void PlaySFX(string clipName)
        {
            if (sfxDict.ContainsKey(clipName))
            {
                AudioClipData clipData = sfxDict[clipName];
                sfxSource.pitch = clipData.pitch;
                sfxSource.PlayOneShot(clipData.clip, masterVolume * sfxVolume * clipData.volume);
            }
            else
            {
                Debug.LogWarning($"SFX clip '{clipName}' not found!");
            }
        }

        public void PlaySFXAtPosition(string clipName, Vector3 position)
        {
            if (sfxDict.ContainsKey(clipName))
            {
                AudioClipData clipData = sfxDict[clipName];
                AudioSource.PlayClipAtPoint(clipData.clip, position, masterVolume * sfxVolume * clipData.volume);
            }
            else
            {
                Debug.LogWarning($"SFX clip '{clipName}' not found!");
            }
        }

        // UI Sound Control
        public void PlayUISound(string clipName)
        {
            if (uiDict.ContainsKey(clipName))
            {
                AudioClipData clipData = uiDict[clipName];
                uiSource.pitch = clipData.pitch;
                uiSource.PlayOneShot(clipData.clip, masterVolume * uiVolume * clipData.volume);
            }
            else
            {
                Debug.LogWarning($"UI sound '{clipName}' not found!");
            }
        }

        // Ambient Sound Control
        public void PlayAmbient(string clipName)
        {
            if (ambientDict.ContainsKey(clipName))
            {
                AudioClipData clipData = ambientDict[clipName];
                ambientSource.clip = clipData.clip;
                ambientSource.volume = masterVolume * ambientVolume * clipData.volume;
                ambientSource.pitch = clipData.pitch;
                ambientSource.loop = clipData.loop;
                ambientSource.Play();
            }
            else
            {
                Debug.LogWarning($"Ambient sound '{clipName}' not found!");
            }
        }

        public void StopAmbient()
        {
            ambientSource.Stop();
        }

        // Volume Control
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            UpdateAudioSourceVolumes();
        }

        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            if (musicSource != null)
                musicSource.volume = masterVolume * musicVolume;
        }

        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            if (sfxSource != null)
                sfxSource.volume = masterVolume * sfxVolume;
        }

        public void SetUIVolume(float volume)
        {
            uiVolume = Mathf.Clamp01(volume);
            if (uiSource != null)
                uiSource.volume = masterVolume * uiVolume;
        }

        public void SetAmbientVolume(float volume)
        {
            ambientVolume = Mathf.Clamp01(volume);
            if (ambientSource != null)
                ambientSource.volume = masterVolume * ambientVolume;
        }

        // Fade Effects
        private IEnumerator FadeOutMusic(float duration = 1f)
        {
            float startVolume = musicSource.volume;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                musicSource.volume = Mathf.Lerp(startVolume, 0f, elapsed / duration);
                yield return null;
            }

            musicSource.volume = 0f;
            musicSource.Stop();
            musicSource.volume = startVolume;
        }

        private IEnumerator FadeInMusic(float duration = 1f)
        {
            float targetVolume = masterVolume * musicVolume;
            musicSource.volume = 0f;
            musicSource.Play();

            float elapsed = 0f;
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                musicSource.volume = Mathf.Lerp(0f, targetVolume, elapsed / duration);
                yield return null;
            }

            musicSource.volume = targetVolume;
        }

        private IEnumerator CrossfadeMusic(AudioClipData newClipData, float duration = 1f)
        {
            float startVolume = musicSource.volume;
            float elapsed = 0f;

            // Fade out current music
            while (elapsed < duration * 0.5f)
            {
                elapsed += Time.deltaTime;
                musicSource.volume = Mathf.Lerp(startVolume, 0f, elapsed / (duration * 0.5f));
                yield return null;
            }

            // Switch to new music
            musicSource.clip = newClipData.clip;
            musicSource.pitch = newClipData.pitch;
            musicSource.loop = newClipData.loop;
            musicSource.Play();

            // Fade in new music
            float targetVolume = masterVolume * musicVolume * newClipData.volume;
            elapsed = 0f;
            while (elapsed < duration * 0.5f)
            {
                elapsed += Time.deltaTime;
                musicSource.volume = Mathf.Lerp(0f, targetVolume, elapsed / (duration * 0.5f));
                yield return null;
            }

            musicSource.volume = targetVolume;
        }

        // Event Handlers
        private void OnMatchEvent(MatchEvent matchEvent)
        {
            if (matchEvent.combo > 1)
            {
                PlaySFX("combo");
            }
            else
            {
                PlaySFX("match");
            }
        }

        private void OnTowerBuilt(TowerBuiltEvent towerEvent)
        {
            PlaySFX("build");
        }

        private void OnEnemyDefeated(EnemyDefeatedEvent enemyEvent)
        {
            PlaySFX("enemy_defeat");
        }

        private void OnLevelStart(LevelStartEvent levelEvent)
        {
            // Play appropriate music based on chapter
            switch (levelEvent.levelData.chapterName)
            {
                case "废墟觉醒":
                    PlayMusic("wasteland_theme");
                    PlayAmbient("wind_ambient");
                    break;
                case "工业遗迹":
                    PlayMusic("industrial_theme");
                    PlayAmbient("machinery_ambient");
                    break;
                case "核心污染区":
                    PlayMusic("danger_theme");
                    PlayAmbient("radiation_ambient");
                    break;
                default:
                    PlayMusic("default_theme");
                    break;
            }
        }

        private void OnLevelComplete(LevelCompleteEvent levelEvent)
        {
            PlaySFX("victory");
            PlayMusic("victory_theme");
        }

        private void OnGameOver(GameOverEvent gameOverEvent)
        {
            PlaySFX("game_over");
            StopMusic();
            StopAmbient();
        }

        private void OnDestroy()
        {
            EventSystem.Unsubscribe<MatchEvent>(OnMatchEvent);
            EventSystem.Unsubscribe<TowerBuiltEvent>(OnTowerBuilt);
            EventSystem.Unsubscribe<EnemyDefeatedEvent>(OnEnemyDefeated);
            EventSystem.Unsubscribe<LevelStartEvent>(OnLevelStart);
            EventSystem.Unsubscribe<LevelCompleteEvent>(OnLevelComplete);
            EventSystem.Unsubscribe<GameOverEvent>(OnGameOver);
        }
    }
}
