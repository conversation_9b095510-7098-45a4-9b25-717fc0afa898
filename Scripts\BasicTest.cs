using UnityEngine;

/// <summary>
/// 最基础的测试脚本 - 确保没有任何编译错误
/// </summary>
public class BasicTest : MonoBehaviour
{
    void Start()
    {
        Debug.Log("=== 基础测试脚本启动成功！===");
        Debug.Log("按 T 键进行测试");
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.T))
        {
            Debug.Log("测试按键正常工作！");
            TestBasicFunctions();
        }

        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            CreateTestCube();
        }
    }

    void TestBasicFunctions()
    {
        Debug.Log("=== 开始基础功能测试 ===");
        
        // 测试相机
        Camera mainCamera = Camera.main;
        if (mainCamera != null)
        {
            Debug.Log("✓ 主相机正常");
        }
        else
        {
            Debug.Log("✗ 主相机未找到");
        }

        // 测试输入
        Debug.Log("✓ 输入系统正常");

        // 测试对象创建
        GameObject testObj = new GameObject("TestObject");
        if (testObj != null)
        {
            Debug.Log("✓ 对象创建正常");
            Destroy(testObj);
        }

        Debug.Log("=== 基础功能测试完成 ===");
    }

    void CreateTestCube()
    {
        GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
        cube.name = "TestCube";
        cube.transform.position = new Vector3(Random.Range(-5, 5), 1, Random.Range(-5, 5));
        
        // 随机颜色
        Renderer renderer = cube.GetComponent<Renderer>();
        Material material = new Material(Shader.Find("Standard"));
        material.color = new Color(Random.value, Random.value, Random.value);
        renderer.material = material;

        Debug.Log("✓ 测试立方体已创建");
    }

    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 200, 100));
        GUILayout.BeginVertical("box");

        GUILayout.Label("基础测试", new GUIStyle(GUI.skin.label) { fontStyle = FontStyle.Bold });
        GUILayout.Label("T - 运行测试");
        GUILayout.Label("1 - 创建立方体");

        if (GUILayout.Button("测试"))
        {
            TestBasicFunctions();
        }

        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
