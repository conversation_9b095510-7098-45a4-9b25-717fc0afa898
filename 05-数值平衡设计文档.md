# 《废土再生：回收者大作战》数值平衡设计文档

## 1. 数值设计原则

### 1.1 核心设计理念
- **渐进成长**: 玩家能力和挑战难度同步提升
- **多元策略**: 不存在唯一最优解，鼓励多样化玩法
- **风险收益**: 高风险操作对应高收益回报
- **时间价值**: 合理的时间投入获得相应的游戏进度

### 1.2 平衡目标
**新手友好**: 前10关保证90%+通过率
**中期挑战**: 11-40关通过率保持在60-80%
**高端内容**: 41-70关通过率40-60%，提供足够挑战
**付费平衡**: 付费优势不超过20%，主要体现在便利性

### 1.3 数值体系架构
```
核心数值层:
├── 基础资源系统 (金属、塑料、电子等)
├── 成长货币系统 (再生核心、经验值)
├── 时间系统 (步数、时间限制)
└── 难度系统 (污染值、敌人强度)

衍生数值层:
├── 建造成本系统
├── 奖励计算系统  
├── 连击倍数系统
└── 成就进度系统
```

## 2. 基础资源系统

### 2.1 资源类型与获取

#### 基础资源获取率
```
资源类型          基础获取量    稀有度    主要用途
金属废料          2-4个/块     ★☆☆☆☆   建造、升级
塑料废料          1-3个/块     ★☆☆☆☆   建造、合成
木材废料          3-5个/块     ★☆☆☆☆   生物燃料
电子废料          1个/块       ★★★☆☆   高级建造
玻璃废料          1-2个/块     ★★☆☆☆   科技研发
有机废料          2-4个/块     ★☆☆☆☆   基地美化
再生核心碎片      0.1个/块     ★★★★☆   核心升级
完整再生核心      特殊获取     ★★★★★   重要建造
```

#### 连击奖励系统
```
连击数量    资源倍数    再生核心奖励概率
3连击      1.0x        0.5%
4连击      1.5x        2%
5连击      2.0x        5%
6连击      2.5x        10%
7连击      3.0x        15%
8+连击     3.5x        20%
```

#### 特殊形状奖励
```
消除形状    奖励倍数    特殊效果
L型         1.2x        获得1个再生核心碎片
T型         1.5x        范围净化效果
十字型      2.0x        获得1个完整再生核心
```

### 2.2 资源消耗平衡

#### 防御设施建造成本
```
设施名称        金属  塑料  电子  玻璃  核心  建造时间
风力过滤器      3     2     0     0     1     即时
声波驱离器      1     0     2     0     1     即时  
护盾发生器      0     0     3     2     2     即时
资源加速器      2     1     1     0     1     即时
激光炮塔        5     0     3     1     3     即时
电磁网发射器    3     2     4     0     2     即时
```

#### 基地建筑成本
```
建筑名称        等级  金属  塑料  电子  玻璃  有机  核心  建造时间
指挥中心        1     0     0     0     0     0     0     0分钟
指挥中心        2     10    5     5     0     0     5     30分钟
指挥中心        3     25    10    15    5     0     15    2小时
指挥中心        4     50    20    30    15    0     30    6小时
指挥中心        5     100   40    60    30    0     60    12小时

资源处理厂      1     8     6     3     0     0     3     1小时
资源处理厂      2     20    15    8     2     0     8     3小时
资源处理厂      3     45    30    20    8     0     20    8小时

研究实验室      1     5     3     10    8     0     5     2小时
研究实验室      2     15    8     25    20    0     15    6小时
研究实验室      3     35    20    60    50    0     35    12小时

居住区          1     10    15    0     0     20    2     1小时
居住区          2     25    35    5     0     50    5     4小时
居住区          3     60    80    15    0     120   15    10小时
```

## 3. 难度曲线设计

### 3.1 关卡难度递增

#### 污染值设计
```
关卡范围    初始污染值    目标污染值    净化要求    难度系数
1-5关       50-100       ≤10          80%+        1.0
6-10关      80-150       ≤15          75%+        1.2
11-15关     120-200      ≤20          70%+        1.5
16-20关     180-280      ≤25          65%+        1.8
21-25关     250-350      ≤30          60%+        2.0
26-30关     320-450      ≤35          55%+        2.3
31-35关     400-550      ≤40          50%+        2.6
36-40关     500-650      ≤45          45%+        3.0
41-50关     600-800      ≤50          40%+        3.5
51-60关     750-1000     ≤55          35%+        4.0
61-70关     900-1200     ≤60          30%+        4.5
```

#### 步数限制设计
```
关卡类型        基础步数    调整系数    最终步数范围
教学关卡        理论步数    ×2.0        40-60步
简单关卡        理论步数    ×1.8        35-50步
普通关卡        理论步数    ×1.5        30-45步
困难关卡        理论步数    ×1.3        25-35步
地狱关卡        理论步数    ×1.1        20-30步
Boss关卡        理论步数    ×1.6        特殊设计
```

### 3.2 敌人强度平衡

#### 基础敌人数值
```
敌人类型      生命值    移动速度    污染伤害    出现频率    特殊能力
污染爬虫      3-8       1格/回合    10          70%         无
腐蚀飞虫      1-3       2格/回合    5           20%         飞行
变异植物      8-15      0.5格/回合  25          8%          死亡爆炸
污染之王      30-100    1格/回合    100         2%          多阶段
```

#### 入侵波次设计
```
触发条件              敌人数量    敌人类型分布              间隔时间
每消除15个废料块      1-3只       爬虫80% 飞虫20%          无
消除污染核心          3-6只       爬虫60% 飞虫30% 植物10%  无
关卡进行到50%         5-10只      爬虫50% 飞虫35% 植物15%  无
Boss关卡特殊触发      1只         污染之王100%             特殊
```

## 4. 成长系统数值

### 4.1 玩家等级系统

#### 经验值获取
```
行为类型            基础经验值    额外条件奖励
完成关卡            50           首次完成+50
获得星级评价        +20/星       满星+30
连击达成            5×连击数     8+连击额外+20
建造防御设施        10           首次建造+20
升级基地建筑        等级×50      无
发现隐藏要素        100          一次性奖励
```

#### 等级提升收益
```
等级    升级所需经验    累计经验    解锁内容
1       0              0           基础功能
2       100            100         好友系统
3       250            350         每日任务
4       450            800         基地装饰
5       700            1500        高级建筑
10      2000           8500        公会功能
15      4000           22500       竞技场
20      6000           45000       自定义关卡
25      8000           77000       全部功能
```

### 4.2 科技树系统

#### 消除技能分支
```
技能名称          等级  研发成本(核心)  效果描述
基础效率强化      1     5              消除步数+5
基础效率强化      2     15             消除步数+10  
基础效率强化      3     35             消除步数+15

连锁反应增强      1     10             连锁范围+1
连锁反应增强      2     25             连锁范围+2
连锁反应增强      3     50             连锁伤害+50%

时间控制          1     20             获得暂停能力(3次/关)
时间控制          2     40             暂停时间延长至30秒
时间控制          3     80             获得时间倒流(1次/关)
```

#### 防御科技分支
```
技能名称          等级  研发成本(核心)  效果描述
设施效率提升      1     8              所有设施效果+20%
设施效率提升      2     20             所有设施效果+40%
设施效率提升      3     45             所有设施效果+60%

建造速度加快      1     12             建造时间-25%
建造速度加快      2     30             建造时间-50%
建造速度加快      3     60             建造时间-75%

自动修复          1     25             设施自动修复(慢速)
自动修复          2     50             修复速度+100%
自动修复          3     100            设施免疫伤害5秒
```

## 5. 经济系统平衡

### 5.1 收入支出平衡

#### 每日收入预期
```
玩家等级    每日游戏时长    基础资源收入    核心收入    经验收入
1-5级       30分钟         金属50 塑料30   2-3个       200-300
6-10级      45分钟         金属80 塑料50   4-6个       400-600
11-15级     60分钟         金属120 塑料80  6-9个       600-900
16-20级     75分钟         金属180 塑料120 8-12个      800-1200
21+级       90分钟         金属250 塑料180 12-18个     1000-1500
```

#### 支出需求分析
```
支出类型        每日消耗量    占总收入比例    重要性
关卡挑战        30-50%        中等           核心玩法
基地建设        20-30%        高             长期目标
科技研发        15-25%        高             能力提升
装饰美化        5-15%         低             个性化
应急道具        5-10%         中等           困难关卡
```

### 5.2 付费点设计

#### 便利性付费
```
商品名称        价格(元)  效果描述                冷却时间
额外步数+5      1         单关卡增加5步           无
额外步数+10     2         单关卡增加10步          无
时间暂停30秒    1         暂停游戏30秒            无
关卡重试        0.5       重新开始当前关卡        无
建造加速        1-5       立即完成建造            无
资源包小        3         金属50 塑料30 核心2     24小时
资源包大        8         金属150 塑料100 核心8   24小时
```

#### 内容付费
```
商品名称        价格(元)  内容描述
角色皮肤包      6-12      5套不同风格的角色外观
建筑装饰包      3-8       特殊的建筑外观和装饰
关卡包          5-10      额外的挑战关卡(10关)
音乐包          2-5       额外的背景音乐
特效包          4-8       华丽的消除和建造特效
```

#### 订阅服务
```
服务类型    价格(元/月)  包含内容
基础月卡    18          每日资源×2, VIP标识, 专属客服
高级月卡    35          基础内容+每日核心×3+专属关卡
年度会员    180         高级内容+所有DLC+优先体验新功能
```

## 6. 平衡性验证

### 6.1 关键指标监控

#### 关卡通过率目标
```
关卡类型        目标通过率    可接受范围    调整阈值
新手教学        95%+          90-100%       <90%需调整
基础关卡        80%+          70-90%        <70%需调整
进阶关卡        65%+          55-75%        <55%需调整
困难关卡        45%+          35-55%        <35%需调整
地狱关卡        25%+          15-35%        <15%需调整
```

#### 资源获取效率
```
资源类型        每小时目标获取量    实际监控范围
金属            80-120个           ±20%
塑料            60-90个            ±20%
电子            8-15个             ±25%
再生核心        3-6个              ±30%
```

### 6.2 动态平衡调整

#### 自适应难度系统
```python
def calculate_dynamic_difficulty(player_data):
    base_difficulty = get_level_base_difficulty(level_id)
    
    # 玩家能力评估
    recent_performance = get_recent_performance(player_data, last_10_games)
    skill_factor = calculate_skill_factor(recent_performance)
    
    # 设备性能评估  
    device_factor = get_device_performance_factor(player_data.device_info)
    
    # 游戏时长评估
    playtime_factor = get_playtime_factor(player_data.total_playtime)
    
    # 付费状态评估
    payment_factor = get_payment_factor(player_data.payment_history)
    
    # 综合计算
    final_difficulty = base_difficulty * skill_factor * device_factor * playtime_factor * payment_factor
    
    # 限制调整范围
    return clamp(final_difficulty, base_difficulty * 0.7, base_difficulty * 1.3)
```

#### 实时数据反馈调整
```
监控周期        调整类型        调整幅度        生效时间
每日            关卡通过率      ±5%            次日更新
每周            资源获取率      ±10%           周更新
每月            整体难度曲线    ±15%           月度更新
特殊事件        紧急调整        ±20%           热更新
```

## 7. A/B测试方案

### 7.1 测试维度

#### 难度测试
```
测试组A: 当前难度设置
测试组B: 难度降低10%
测试组C: 难度提高10%

监控指标:
- 关卡通过率
- 平均重试次数  
- 游戏时长
- 付费转化率
- 用户留存率
```

#### 奖励测试
```
测试组A: 当前奖励设置
测试组B: 奖励增加20%
测试组C: 奖励减少15%

监控指标:
- 玩家满意度
- 游戏进度速度
- 付费意愿
- 长期留存
```

### 7.2 测试执行

#### 测试流程
1. **假设制定**: 明确测试目标和预期结果
2. **样本分配**: 随机分配用户到不同测试组
3. **数据收集**: 持续监控关键指标变化
4. **统计分析**: 使用统计学方法验证显著性
5. **结果应用**: 根据测试结果调整游戏参数

#### 测试周期
- **短期测试**: 1-2周，验证即时影响
- **中期测试**: 1个月，观察适应性变化  
- **长期测试**: 3个月，评估长期影响

## 8. 数值工具与流程

### 8.1 数值配置工具

#### Excel配置表
```
关卡配置表:
- LevelID, ChapterName, LevelName, Difficulty
- GridSize, InitialPollution, TargetPollution
- StepLimit, TimeLimit, EnemyWaves
- BasicReward, StarRewards

资源配置表:
- ResourceType, BaseAmount, RarityLevel
- DropRate, ComboMultiplier, SpecialEffects

建筑配置表:
- BuildingID, BuildingName, BuildingType
- CostResources, BuildTime, Effects
- UpgradePath, MaxLevel
```

#### 数值验证工具
```python
class BalanceValidator:
    def validate_level_difficulty(self, level_data):
        # 验证难度曲线是否合理
        pass
    
    def validate_resource_economy(self, income_data, expense_data):
        # 验证资源收支平衡
        pass
    
    def validate_progression_speed(self, player_data):
        # 验证成长速度是否合适
        pass
```

### 8.2 数据监控系统

#### 实时监控面板
- 关卡通过率实时统计
- 资源获取分布图表
- 玩家行为热力图
- 付费转化漏斗分析

#### 预警机制
- 通过率异常预警 (偏离目标±20%)
- 资源通胀预警 (获取速度过快)
- 付费异常预警 (转化率突然变化)
- 用户流失预警 (留存率下降)

---

*数值平衡是游戏体验的核心，通过科学的数值设计和持续的数据监控，确保游戏既有足够的挑战性，又能给玩家带来持续的成就感。合理的经济系统和付费设计，能够实现玩家满意度和商业收益的双赢。*
