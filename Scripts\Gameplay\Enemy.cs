using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    public class Enemy : MonoBehaviour
    {
        [Header("Enemy Data")]
        public EnemyType enemyType;
        public int maxHealth = 10;
        public float moveSpeed = 1f;
        public int pollutionDamage = 10;

        [Header("Visual")]
        public Renderer enemyRenderer;
        public GameObject healthBar;
        public ParticleSystem deathEffect;
        public GameObject statusEffectIndicator;

        [Header("Animation")]
        public float bobHeight = 0.1f;
        public float bobSpeed = 2f;

        private EnemyData enemyData;
        private int currentHealth;
        private List<Vector2Int> path;
        private int currentPathIndex = 0;
        private Vector3 targetPosition;
        private Vector3 originalPosition;
        private bool isDead = false;
        private bool hasReachedTarget = false;
        private GridSystem gridSystem;

        // Status effects
        private float slowEffectEndTime = 0f;
        private float slowMultiplier = 1f;
        private float electromagneticEffectEndTime = 0f;
        private bool isStunned = false;

        // Movement
        private float moveTimer = 0f;
        private float moveInterval = 1f; // Move every second

        public void Initialize(EnemyData data)
        {
            enemyData = data;
            enemyType = data.enemyType;
            maxHealth = data.health;
            currentHealth = maxHealth;
            moveSpeed = data.moveSpeed;
            pollutionDamage = data.pollutionDamage;
            path = new List<Vector2Int>(data.path);
            
            currentPathIndex = 0;
            isDead = false;
            hasReachedTarget = false;
            
            gridSystem = FindObjectOfType<GridSystem>();
            
            SetupVisuals();
            SetupMovement();
        }

        private void SetupVisuals()
        {
            if (enemyRenderer == null)
                enemyRenderer = GetComponent<Renderer>();

            if (enemyRenderer != null)
            {
                Color enemyColor = GetEnemyColor();
                enemyRenderer.material.color = enemyColor;
            }

            UpdateHealthBar();
            
            if (statusEffectIndicator != null)
                statusEffectIndicator.SetActive(false);

            originalPosition = transform.position;
        }

        private Color GetEnemyColor()
        {
            switch (enemyType)
            {
                case EnemyType.PollutionCrawler:
                    return new Color(0.6f, 0.4f, 0.2f); // Brown
                case EnemyType.CorrosiveFly:
                    return new Color(0.8f, 0.8f, 0.2f); // Yellow-green
                case EnemyType.MutatedPlant:
                    return new Color(0.4f, 0.2f, 0.6f); // Purple
                case EnemyType.PollutionKing:
                    return new Color(0.2f, 0.1f, 0.1f); // Dark red
                default:
                    return Color.gray;
            }
        }

        private void SetupMovement()
        {
            moveInterval = 1f / moveSpeed;
            
            if (path.Count > 0)
            {
                Vector2Int firstTarget = path[0];
                targetPosition = gridSystem.GridToWorldPosition(firstTarget);
            }
            else
            {
                // Default path to purifier
                Vector2Int purifierPos = gridSystem.GetPurifierPosition();
                path.Add(purifierPos);
                targetPosition = gridSystem.GridToWorldPosition(purifierPos);
            }
        }

        public void UpdateEnemy()
        {
            if (isDead || hasReachedTarget) return;

            UpdateStatusEffects();
            UpdateMovement();
            UpdateAnimations();
        }

        private void UpdateStatusEffects()
        {
            // Update slow effect
            if (Time.time > slowEffectEndTime)
            {
                slowMultiplier = 1f;
            }

            // Update electromagnetic effect
            if (Time.time > electromagneticEffectEndTime)
            {
                isStunned = false;
            }

            // Update status indicator
            bool hasStatusEffect = slowMultiplier < 1f || isStunned;
            if (statusEffectIndicator != null)
            {
                statusEffectIndicator.SetActive(hasStatusEffect);
            }
        }

        private void UpdateMovement()
        {
            if (isStunned) return;

            moveTimer += Time.deltaTime;
            float effectiveMoveInterval = moveInterval / slowMultiplier;

            if (moveTimer >= effectiveMoveInterval)
            {
                MoveToNextPosition();
                moveTimer = 0f;
            }
        }

        private void MoveToNextPosition()
        {
            if (currentPathIndex >= path.Count)
            {
                hasReachedTarget = true;
                return;
            }

            // Move to current target
            Vector2Int currentTarget = path[currentPathIndex];
            Vector3 worldTarget = gridSystem.GridToWorldPosition(currentTarget);
            
            // Smooth movement
            StartCoroutine(SmoothMoveTo(worldTarget));
            
            currentPathIndex++;
            
            // Check if reached final destination
            if (currentPathIndex >= path.Count)
            {
                hasReachedTarget = true;
            }
        }

        private IEnumerator SmoothMoveTo(Vector3 target)
        {
            Vector3 startPos = transform.position;
            float duration = moveInterval / slowMultiplier * 0.8f; // Slightly faster for smooth appearance
            float elapsed = 0f;

            while (elapsed < duration && !isDead)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                
                // Smooth interpolation
                transform.position = Vector3.Lerp(startPos, target, t);
                
                yield return null;
            }

            if (!isDead)
            {
                transform.position = target;
                originalPosition = target;
            }
        }

        private void UpdateAnimations()
        {
            if (isDead) return;

            // Bobbing animation
            float bobOffset = Mathf.Sin(Time.time * bobSpeed) * bobHeight;
            Vector3 bobPosition = originalPosition + Vector3.up * bobOffset;
            
            // Only apply bob if not currently moving smoothly
            if (Vector3.Distance(transform.position, originalPosition) < 0.1f)
            {
                transform.position = bobPosition;
            }

            // Rotation for flying enemies
            if (enemyType == EnemyType.CorrosiveFly)
            {
                transform.Rotate(Vector3.up, 90f * Time.deltaTime);
            }

            // Pulsing for boss enemies
            if (enemyType == EnemyType.PollutionKing)
            {
                float scale = 1f + Mathf.Sin(Time.time * 3f) * 0.1f;
                transform.localScale = Vector3.one * scale;
            }
        }

        public void TakeDamage(int damage)
        {
            if (isDead) return;

            currentHealth -= damage;
            currentHealth = Mathf.Max(0, currentHealth);
            
            UpdateHealthBar();
            
            // Flash effect
            StartCoroutine(DamageFlash());
            
            if (currentHealth <= 0)
            {
                Die();
            }
        }

        private IEnumerator DamageFlash()
        {
            if (enemyRenderer != null)
            {
                Color originalColor = enemyRenderer.material.color;
                enemyRenderer.material.color = Color.red;
                
                yield return new WaitForSeconds(0.1f);
                
                enemyRenderer.material.color = originalColor;
            }
        }

        private void Die()
        {
            if (isDead) return;
            
            isDead = true;
            
            // Play death effect
            if (deathEffect != null)
            {
                ParticleSystem effect = Instantiate(deathEffect, transform.position, Quaternion.identity);
                effect.Play();
                Destroy(effect.gameObject, 2f);
            }
            
            // Hide enemy
            gameObject.SetActive(false);
        }

        public void ApplySlowEffect(float duration, float slowAmount)
        {
            slowEffectEndTime = Time.time + duration;
            slowMultiplier = slowAmount;
        }

        public void ApplyElectromagneticEffect(float duration)
        {
            electromagneticEffectEndTime = Time.time + duration;
            isStunned = true;
        }

        private void UpdateHealthBar()
        {
            if (healthBar != null)
            {
                float healthPercent = (float)currentHealth / maxHealth;
                healthBar.transform.localScale = new Vector3(healthPercent, 1f, 1f);
                
                // Change color based on health
                Renderer healthRenderer = healthBar.GetComponent<Renderer>();
                if (healthRenderer != null)
                {
                    if (healthPercent > 0.6f)
                        healthRenderer.material.color = Color.green;
                    else if (healthPercent > 0.3f)
                        healthRenderer.material.color = Color.yellow;
                    else
                        healthRenderer.material.color = Color.red;
                }
            }
        }

        public bool IsDead()
        {
            return isDead;
        }

        public bool HasReachedTarget()
        {
            return hasReachedTarget;
        }

        public int GetCurrentHealth()
        {
            return currentHealth;
        }

        public int GetMaxHealth()
        {
            return maxHealth;
        }

        public int GetPollutionDamage()
        {
            return pollutionDamage;
        }

        public EnemyType GetEnemyType()
        {
            return enemyType;
        }

        public float GetMoveSpeed()
        {
            return moveSpeed * slowMultiplier;
        }

        public bool IsSlowed()
        {
            return slowMultiplier < 1f;
        }

        public bool IsStunned()
        {
            return isStunned;
        }

        public Vector2Int GetCurrentGridPosition()
        {
            return gridSystem.WorldToGridPosition(transform.position);
        }

        public Vector2Int GetNextTargetPosition()
        {
            if (currentPathIndex < path.Count)
            {
                return path[currentPathIndex];
            }
            return gridSystem.GetPurifierPosition();
        }

        public List<Vector2Int> GetRemainingPath()
        {
            if (currentPathIndex < path.Count)
            {
                return path.GetRange(currentPathIndex, path.Count - currentPathIndex);
            }
            return new List<Vector2Int>();
        }

        public string GetEnemyDescription()
        {
            switch (enemyType)
            {
                case EnemyType.PollutionCrawler:
                    return "污染爬虫 - 基础敌人，移动速度中等";
                case EnemyType.CorrosiveFly:
                    return "腐蚀飞虫 - 飞行单位，可越过障碍";
                case EnemyType.MutatedPlant:
                    return "变异植物 - 移动缓慢但生命值高";
                case EnemyType.PollutionKing:
                    return "污染之王 - Boss级敌人，免疫控制效果";
                default:
                    return "未知敌人";
            }
        }

        private void OnMouseEnter()
        {
            // Show enemy info on hover
            if (healthBar != null)
            {
                healthBar.SetActive(true);
            }
        }

        private void OnMouseExit()
        {
            // Hide enemy info
            if (healthBar != null && currentHealth == maxHealth)
            {
                healthBar.SetActive(false);
            }
        }

        private void OnDestroy()
        {
            // Cleanup any ongoing coroutines
            StopAllCoroutines();
        }
    }
}
