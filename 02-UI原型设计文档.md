# 《废土再生：回收者大作战》UI/UX原型设计文档

## 1. 设计原则与风格指南

### 1.1 核心设计理念
- **直观易懂**: 图标和界面元素一目了然
- **情感共鸣**: 通过视觉传达希望与重生的主题
- **操作流畅**: 减少点击层级，优化操作路径
- **视觉层次**: 清晰的信息架构和视觉引导

### 1.2 视觉风格定义
**色彩方案**:
- **主色调**: 
  - 废土棕 (#8B4513) - 代表污染和破坏
  - 净化绿 (#32CD32) - 代表希望和重生
  - 科技蓝 (#1E90FF) - 代表技术和未来
- **辅助色**:
  - 警告橙 (#FF8C00) - 危险提示
  - 成功金 (#FFD700) - 成就和奖励
  - 中性灰 (#708090) - 背景和分割

**字体规范**:
- **主标题**: 粗体，24-32px，科技感字体
- **副标题**: 中等粗细，18-24px
- **正文**: 常规，14-16px，易读性优先
- **数值**: 等宽字体，便于对齐

### 1.3 图标设计规范
**图标风格**: 线性图标+填充色，2px描边
**尺寸规格**: 24x24, 32x32, 48x48, 64x64px
**设计原则**: 简洁明了，符合用户认知习惯

## 2. 主要界面原型设计

### 2.1 启动界面 (Splash Screen)

```
┌─────────────────────────────────────┐
│                                     │
│           [游戏LOGO]                │
│      废土再生：回收者大作战           │
│                                     │
│         [加载进度条]                │
│           Loading...                │
│                                     │
│    [版本号]              [公司LOGO]  │
└─────────────────────────────────────┘
```

**设计要点**:
- 背景：废土景观渐变到绿色净化区域
- LOGO：金属质感+绿色光效
- 加载条：能量充能效果
- 音效：低沉的环境音+希望主题音乐渐入

### 2.2 主菜单界面

```
┌─────────────────────────────────────┐
│  [设置]              [商店] [邮件]   │
│                                     │
│           [游戏标题]                │
│                                     │
│         ┌─────────────┐             │
│         │  开始游戏   │             │
│         └─────────────┘             │
│         ┌─────────────┐             │
│         │  基地建设   │             │
│         └─────────────┘             │
│         ┌─────────────┐             │
│         │  图鉴收集   │             │
│         └─────────────┘             │
│         ┌─────────────┐             │
│         │  每日挑战   │             │
│         └─────────────┘             │
│                                     │
│  [玩家头像] [等级] [金币] [钻石]     │
└─────────────────────────────────────┘
```

**交互设计**:
- 按钮悬停效果：轻微放大+发光边框
- 背景动画：缓慢的粒子飘动效果
- 音效：按钮点击有机械音效

### 2.3 关卡选择界面

```
┌─────────────────────────────────────┐
│ [返回] 第一章：废墟觉醒    [章节菜单] │
│                                     │
│  ○─○─○─○─○                        │
│  1 2 3 4 5                         │
│  ★★★ ★★☆ ★☆☆ ☆☆☆ 🔒              │
│                                     │
│  ○─○─○─○─○                        │
│  6 7 8 9 10                        │
│  ★★★ ★★☆ ☆☆☆ 🔒 🔒                │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 关卡3：时间压力                 │ │
│ │ 难度：★★☆                      │ │
│ │ 最佳：42步                      │ │
│ │ 奖励：金属×5, 塑料×3           │ │
│ │          [开始挑战]             │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

**视觉特效**:
- 已完成关卡：绿色光环+植被生长动画
- 当前关卡：脉冲光效
- 未解锁关卡：灰色+锁链效果
- 路径连线：能量流动动画

### 2.4 主游戏界面

```
┌─────────────────────────────────────┐
│ 污染值: 45/100 ████░░░░  步数: 23   │
│ 金属:12 塑料:8 电子:3    [暂停][道具]│
├─────────────────────────────────────┤
│ ┌─┬─┬─┬─┬─┬─┬─┬─┐                 │
│ │M│P│M│E│M│P│G│M│                 │
│ ├─┼─┼─┼─┼─┼─┼─┼─┤                 │
│ │P│M│P│M│P│M│P│E│                 │
│ ├─┼─┼─┼─┼─┼─┼─┼─┤                 │
│ │M│P│X│M│P│M│M│P│  X=污染核心      │
│ ├─┼─┼─┼─┼─┼─┼─┼─┤  M=金属废料      │
│ │P│M│P│M│P│T│P│M│  P=塑料废料      │
│ ├─┼─┼─┼─┼─┼─┼─┼─┤  E=电子废料      │
│ │M│P│M│P│M│P│M│P│  T=防御塔        │
│ ├─┼─┼─┼─┼─┼─┼─┼─┤  G=玻璃废料      │
│ │P│M│P│M│P│M│P│M│                 │
│ ├─┼─┼─┼─┼─┼─┼─┼─┤                 │
│ │M│P│M│P│M│P│M│P│                 │
│ ├─┼─┼─┼─┼─┼─┼─┼─┤                 │
│ │P│M│P│M│P│M│P│[净]│ [净]=净化装置  │
│ └─┴─┴─┴─┴─┴─┴─┴─┘                 │
├─────────────────────────────────────┤
│[风力] [声波] [护盾] [加速]  [技能栏] │
└─────────────────────────────────────┘
```

**交互设计**:
- 废料块选择：高亮边框+轻微浮起效果
- 可消除提示：闪烁光效
- 连消预览：虚线连接线
- 建造模式：半透明预览+范围指示器

### 2.5 基地建设界面

```
┌─────────────────────────────────────┐
│ [返回] 我的基地 - 等级5    [升级]    │
│                                     │
│     🏭        🔬        🏠          │
│   资源厂    研究所    居住区         │
│   Lv.3      Lv.2      Lv.4          │
│                                     │
│        🏢                           │
│      指挥中心                       │
│       Lv.5                          │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │ 资源处理厂 Lv.3                │ │
│  │ 效果：转化效率 +30%             │ │
│  │ 升级需要：                      │ │
│  │ 金属×20, 电子×10, 核心×5       │ │
│  │          [升级] [详情]          │ │
│  └─────────────────────────────────┘ │
│                                     │
│ 资源: 金属:156 塑料:89 电子:23      │
│       核心:12 人口:45/60            │
└─────────────────────────────────────┘
```

**3D视觉效果**:
- 等距视角，可旋转查看
- 建筑升级动画：能量注入+结构变化
- 环境效果：昼夜循环，天气变化
- 人员活动：小人物走动，增加生活感

### 2.6 商店界面

```
┌─────────────────────────────────────┐
│ [返回] 回收商店           [充值]     │
│                                     │
│ [道具] [资源] [装饰] [限时]          │
│                                     │
│ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐     │
│ │额外 │ │资源 │ │时间 │ │连击 │     │
│ │步数 │ │加速 │ │暂停 │ │增强 │     │
│ │ +5  │ │ x2  │ │ 30s │ │ x2  │     │
│ │ ¥1  │ │ ¥2  │ │ ¥1  │ │ ¥3  │     │
│ └─────┘ └─────┘ └─────┘ └─────┘     │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 月度通行证                      │ │
│ │ ┌─┬─┬─┬─┬─┬─┬─┬─┬─┬─┐       │ │
│ │ │✓│✓│✓│○│○│○│○│○│○│○│       │ │
│ │ └─┴─┴─┴─┴─┴─┴─┴─┴─┴─┘       │ │
│ │ 进度: 3/10  下一奖励: 稀有核心  │ │
│ │          [购买通行证 ¥18]       │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

**购买流程优化**:
- 一键购买，减少确认步骤
- 清晰的价格显示和优惠标识
- 购买后即时生效的视觉反馈

## 3. 交互流程设计

### 3.1 新手引导流程

```
启动游戏 → 观看开场动画 → 创建角色 → 
基础操作教学 → 第一次消除 → 连锁反应演示 → 
建造防御设施 → 抵御第一波敌人 → 基地介绍 → 
完成第一关 → 获得奖励 → 解锁功能提示
```

**引导设计原则**:
- 渐进式披露：一次只教一个概念
- 实际操作：让玩家亲自体验，而非纯演示
- 即时反馈：每个操作都有明确的结果
- 可跳过：老玩家可以快速跳过引导

### 3.2 关卡游戏流程

```
选择关卡 → 查看目标 → 进入游戏 → 
分析局面 → 执行消除 → 建造防御 → 
应对入侵 → 调整策略 → 完成目标 → 
结算奖励 → 升级提示 → 下一关卡
```

### 3.3 基地建设流程

```
进入基地 → 查看资源 → 选择建筑 → 
确认建造 → 等待完成 → 升级建筑 → 
解锁新功能 → 规划布局 → 优化效率
```

## 4. 响应式设计

### 4.1 多屏幕适配

**手机竖屏 (375x667)**:
- 游戏网格占屏幕60%
- 状态栏紧凑显示
- 按钮适当放大便于点击

**手机横屏 (667x375)**:
- 游戏网格居中
- 状态信息分布两侧
- 功能按钮排列在底部

**平板 (768x1024)**:
- 更大的游戏网格
- 详细的状态信息显示
- 多列布局优化空间利用

### 4.2 无障碍设计

**视觉辅助**:
- 色盲友好的配色方案
- 高对比度模式
- 字体大小调节

**操作辅助**:
- 语音提示功能
- 简化操作模式
- 自动瞄准辅助

## 5. 动效设计规范

### 5.1 基础动效

**按钮交互**:
- 点击：缩放0.95倍，持续100ms
- 悬停：轻微发光，渐变300ms
- 禁用：灰度化，透明度50%

**页面切换**:
- 滑入滑出：300ms缓动曲线
- 淡入淡出：200ms线性
- 弹性效果：400ms弹性曲线

### 5.2 游戏特效

**消除动效**:
- 废料消失：粒子爆炸+光效，500ms
- 连锁反应：能量波扩散，800ms
- 特殊消除：独特动画，1000ms

**建造动效**:
- 设施建造：从地面升起，1200ms
- 升级效果：能量注入，800ms
- 损坏修复：重组动画，600ms

## 6. 音效与反馈设计

### 6.1 音效分类

**UI音效**:
- 按钮点击：清脆的机械音
- 页面切换：柔和的过渡音
- 成功提示：上升和弦
- 错误提示：低沉提示音

**游戏音效**:
- 废料消除：材质相关的破碎音
- 连锁反应：递增的音调
- 建造音效：机械组装音
- 战斗音效：能量武器音效

### 6.2 触觉反馈

**振动模式**:
- 轻微振动：按钮点击
- 中等振动：消除成功
- 强烈振动：连锁反应
- 节奏振动：Boss战斗

## 7. 性能优化

### 7.1 UI性能

**渲染优化**:
- UI元素对象池
- 动态加载非关键UI
- 合理的Draw Call控制
- 纹理压缩和合并

**内存管理**:
- 及时释放不用的UI资源
- 图片资源分级加载
- 音效资源预加载策略

### 7.2 用户体验优化

**加载优化**:
- 关键资源优先加载
- 后台预加载下一关卡
- 加载进度可视化
- 加载失败重试机制

**操作优化**:
- 减少不必要的确认弹窗
- 智能的默认选项
- 快捷操作支持
- 误操作撤销功能

---

*UI/UX设计是玩家体验的第一印象，每个界面都应该直观易用，视觉美观，并且能够有效传达游戏的核心价值和情感主题。通过精心设计的交互流程和视觉反馈，确保玩家能够轻松上手并持续享受游戏乐趣。*
