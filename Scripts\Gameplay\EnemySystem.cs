using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    public class EnemySystem : MonoBehaviour
    {
        [Header("Enemy Prefabs")]
        public GameObject[] enemyPrefabs;

        [Header("Spawn Settings")]
        public Transform enemyContainer;
        public float spawnDelay = 1f;

        private List<Enemy> activeEnemies = new List<Enemy>();
        private List<EnemyWaveData> currentWaves = new List<EnemyWaveData>();
        private GridSystem gridSystem;
        private GameConfig gameConfig;
        private ObjectPool<Enemy>[] enemyPools;

        // Wave management
        private int currentWaveIndex = 0;
        private float gameStartTime;
        private bool wavesActive = false;

        public System.Action<Enemy> OnEnemySpawned;
        public System.Action<Enemy> OnEnemyDefeated;
        public System.Action<Enemy> OnEnemyReachedTarget;

        public void Initialize()
        {
            gridSystem = FindObjectOfType<GridSystem>();
            gameConfig = new GameConfig();
            
            if (enemyContainer == null)
            {
                GameObject container = new GameObject("Enemies");
                enemyContainer = container.transform;
                enemyContainer.SetParent(transform);
            }

            InitializeEnemyPools();
            
            // Subscribe to events
            EventSystem.Subscribe<LevelStartEvent>(OnLevelStarted);
        }

        private void InitializeEnemyPools()
        {
            enemyPools = new ObjectPool<Enemy>[enemyPrefabs.Length];
            
            for (int i = 0; i < enemyPrefabs.Length; i++)
            {
                if (enemyPrefabs[i] != null)
                {
                    Enemy enemyComponent = enemyPrefabs[i].GetComponent<Enemy>();
                    if (enemyComponent != null)
                    {
                        enemyPools[i] = new ObjectPool<Enemy>(enemyComponent, 10, enemyContainer);
                    }
                }
            }
        }

        private void Update()
        {
            if (wavesActive)
            {
                UpdateWaveSpawning();
            }

            UpdateEnemies();
            CleanupDeadEnemies();
        }

        public void SetupWaves(List<EnemyWaveData> waves)
        {
            currentWaves = new List<EnemyWaveData>(waves);
            currentWaveIndex = 0;
            wavesActive = false;
        }

        public void StartWaves()
        {
            wavesActive = true;
            gameStartTime = Time.time;
            currentWaveIndex = 0;
        }

        private void UpdateWaveSpawning()
        {
            float currentTime = Time.time - gameStartTime;

            while (currentWaveIndex < currentWaves.Count)
            {
                EnemyWaveData wave = currentWaves[currentWaveIndex];
                
                if (currentTime >= wave.spawnTime)
                {
                    StartCoroutine(SpawnWave(wave));
                    currentWaveIndex++;
                }
                else
                {
                    break;
                }
            }

            // Check if all waves are complete
            if (currentWaveIndex >= currentWaves.Count)
            {
                wavesActive = false;
            }
        }

        private IEnumerator SpawnWave(EnemyWaveData waveData)
        {
            for (int i = 0; i < waveData.count; i++)
            {
                Vector2Int spawnPos = GetSpawnPosition(waveData);
                SpawnEnemy(waveData.enemyType, spawnPos, waveData.path);
                
                if (i < waveData.count - 1)
                {
                    yield return new WaitForSeconds(waveData.spawnInterval);
                }
            }
        }

        private Vector2Int GetSpawnPosition(EnemyWaveData waveData)
        {
            if (waveData.spawnPositions.Count > 0)
            {
                int randomIndex = Random.Range(0, waveData.spawnPositions.Count);
                return waveData.spawnPositions[randomIndex];
            }
            
            // Default spawn at edge of grid
            return new Vector2Int(0, Random.Range(0, gridSystem.gridHeight));
        }

        public void SpawnEnemy(EnemyType enemyType, Vector2Int spawnPosition, List<Vector2Int> path)
        {
            Enemy enemy = GetEnemyFromPool(enemyType);
            if (enemy == null) return;

            // Initialize enemy
            var enemyData = CreateEnemyData(enemyType, spawnPosition, path);
            enemy.Initialize(enemyData);

            // Position enemy
            Vector3 worldPos = gridSystem.GridToWorldPosition(spawnPosition);
            enemy.transform.position = worldPos;

            // Add to active list
            activeEnemies.Add(enemy);

            // Publish spawn event
            EventSystem.Publish(new EnemySpawnEvent
            {
                enemy = enemy,
                spawnPosition = spawnPosition
            });

            OnEnemySpawned?.Invoke(enemy);

            Debug.Log($"Spawned {enemyType} at {spawnPosition}");
        }

        private Enemy GetEnemyFromPool(EnemyType enemyType)
        {
            int poolIndex = (int)enemyType;
            if (poolIndex >= 0 && poolIndex < enemyPools.Length && enemyPools[poolIndex] != null)
            {
                return enemyPools[poolIndex].Get();
            }

            // Fallback: instantiate directly
            GameObject prefab = GetEnemyPrefab(enemyType);
            if (prefab != null)
            {
                GameObject enemyObj = Instantiate(prefab, enemyContainer);
                return enemyObj.GetComponent<Enemy>();
            }

            return null;
        }

        private GameObject GetEnemyPrefab(EnemyType enemyType)
        {
            int index = (int)enemyType;
            if (index >= 0 && index < enemyPrefabs.Length)
            {
                return enemyPrefabs[index];
            }
            return null;
        }

        private EnemyData CreateEnemyData(EnemyType enemyType, Vector2Int spawnPosition, List<Vector2Int> path)
        {
            var config = gameConfig.enemyConfigs[enemyType];
            
            return new EnemyData
            {
                enemyType = enemyType,
                health = config.baseHealth,
                moveSpeed = config.baseMoveSpeed,
                pollutionDamage = config.pollutionDamage,
                path = new List<Vector2Int>(path),
                specialProperties = new Dictionary<string, object>(config.specialAbilities)
            };
        }

        private void UpdateEnemies()
        {
            foreach (var enemy in activeEnemies)
            {
                if (enemy != null && enemy.gameObject.activeInHierarchy)
                {
                    enemy.UpdateEnemy();
                    
                    // Check if enemy reached target
                    if (enemy.HasReachedTarget())
                    {
                        HandleEnemyReachedTarget(enemy);
                    }
                }
            }
        }

        private void CleanupDeadEnemies()
        {
            for (int i = activeEnemies.Count - 1; i >= 0; i--)
            {
                Enemy enemy = activeEnemies[i];
                
                if (enemy == null || !enemy.gameObject.activeInHierarchy || enemy.IsDead())
                {
                    if (enemy != null && enemy.IsDead())
                    {
                        HandleEnemyDefeated(enemy);
                    }
                    
                    activeEnemies.RemoveAt(i);
                }
            }
        }

        private void HandleEnemyReachedTarget(Enemy enemy)
        {
            // Apply pollution damage
            int pollutionDamage = enemy.GetPollutionDamage();
            GameManager.Instance.IncreasePollution(pollutionDamage);

            // Publish event
            EventSystem.Publish(new EnemyReachedEvent
            {
                enemy = enemy,
                pollutionDamage = pollutionDamage
            });

            OnEnemyReachedTarget?.Invoke(enemy);

            // Remove enemy
            RemoveEnemy(enemy);
        }

        private void HandleEnemyDefeated(Enemy enemy)
        {
            Vector2Int position = gridSystem.WorldToGridPosition(enemy.transform.position);
            
            // Publish event
            EventSystem.Publish(new EnemyDefeatedEvent
            {
                enemy = enemy,
                position = position
            });

            OnEnemyDefeated?.Invoke(enemy);

            // Remove enemy
            RemoveEnemy(enemy);
        }

        private void RemoveEnemy(Enemy enemy)
        {
            if (enemy != null)
            {
                // Return to pool or destroy
                EnemyType enemyType = enemy.GetEnemyType();
                int poolIndex = (int)enemyType;
                
                if (poolIndex >= 0 && poolIndex < enemyPools.Length && enemyPools[poolIndex] != null)
                {
                    enemyPools[poolIndex].Return(enemy);
                }
                else
                {
                    Destroy(enemy.gameObject);
                }
            }
        }

        public List<Enemy> GetActiveEnemies()
        {
            return new List<Enemy>(activeEnemies);
        }

        public List<Enemy> GetEnemiesInRange(Vector2Int center, float range)
        {
            List<Enemy> enemiesInRange = new List<Enemy>();
            
            foreach (var enemy in activeEnemies)
            {
                if (enemy != null)
                {
                    Vector2Int enemyPos = gridSystem.WorldToGridPosition(enemy.transform.position);
                    float distance = Vector2Int.Distance(center, enemyPos);
                    
                    if (distance <= range)
                    {
                        enemiesInRange.Add(enemy);
                    }
                }
            }
            
            return enemiesInRange;
        }

        public Enemy GetNearestEnemy(Vector2Int position)
        {
            Enemy nearestEnemy = null;
            float nearestDistance = float.MaxValue;
            
            foreach (var enemy in activeEnemies)
            {
                if (enemy != null)
                {
                    Vector2Int enemyPos = gridSystem.WorldToGridPosition(enemy.transform.position);
                    float distance = Vector2Int.Distance(position, enemyPos);
                    
                    if (distance < nearestDistance)
                    {
                        nearestDistance = distance;
                        nearestEnemy = enemy;
                    }
                }
            }
            
            return nearestEnemy;
        }

        public void ClearAllEnemies()
        {
            foreach (var enemy in activeEnemies)
            {
                if (enemy != null)
                {
                    RemoveEnemy(enemy);
                }
            }
            activeEnemies.Clear();
        }

        public int GetActiveEnemyCount()
        {
            return activeEnemies.Count;
        }

        public bool AreWavesComplete()
        {
            return !wavesActive && activeEnemies.Count == 0;
        }

        private void OnLevelStarted(LevelStartEvent levelEvent)
        {
            ClearAllEnemies();
            SetupWaves(levelEvent.levelData.enemyWaves);
            StartWaves();
        }

        private void OnDestroy()
        {
            EventSystem.Unsubscribe<LevelStartEvent>(OnLevelStarted);
            
            // Clear object pools
            if (enemyPools != null)
            {
                foreach (var pool in enemyPools)
                {
                    pool?.Clear();
                }
            }
        }
    }
}
