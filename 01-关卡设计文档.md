# 《废土再生：回收者大作战》关卡设计文档

## 1. 关卡设计总体原则

### 1.1 设计理念
- **渐进式学习**: 每个关卡引入1-2个新概念，避免信息过载
- **多样化挑战**: 不同类型的目标和限制条件，保持新鲜感
- **情感曲线**: 紧张-放松-高潮的节奏设计
- **视觉叙事**: 通过关卡环境讲述废土重建的故事

### 1.2 难度曲线设计
```
难度值计算公式：
基础难度 = (污染值 / 步数限制) × 特殊元素系数 × 地形复杂度
```

**难度等级划分**:
- 简单(1-2): 新手教学，重点体验爽感
- 普通(3-4): 需要基础策略思考
- 困难(5-6): 需要精确计算和规划
- 地狱(7-8): 极限挑战，需要完美操作
- 传说(9-10): 特殊机制，创新玩法

## 2. 第一章：废墟觉醒 (关卡1-10)

### 2.1 教学关卡设计

#### 关卡1: 初次觉醒
**学习目标**: 基础消除操作
**地图布局**: 6x6网格，简单矩形
**废料配置**:
- 金属废料: 60% (蓝色)
- 塑料废料: 40% (绿色)
- 无特殊废料

**具体布局**:
```
[金][塑][金][塑][金][塑]
[塑][金][塑][金][塑][金]
[金][塑][金][塑][金][塑]
[塑][金][塑][金][塑][金]
[金][塑][金][塑][金][塑]
[塑][金][塑][金][塑][金]
```

**目标条件**:
- 初始污染值: 50
- 目标污染值: ≤10
- 步数限制: 50步
- 无敌人入侵

**引导设计**:
1. 手指动画指示第一次消除位置
2. 高亮显示可消除的废料组合
3. 实时显示污染值变化
4. 成功后播放净化动画

#### 关卡2: 连锁反应
**学习目标**: 连锁消除机制
**地图布局**: 6x6网格
**废料配置**:
- 基础废料: 80%
- 爆炸废料: 20% (红色，消除时影响周围3x3)

**特殊机制**:
- 预设3个爆炸废料形成连锁反应链
- 玩家需要找到正确的触发点

**目标条件**:
- 初始污染值: 80
- 目标污染值: ≤15
- 步数限制: 45步
- 必须触发至少1次连锁反应

#### 关卡3: 时间压力
**学习目标**: 时间限制机制
**地图布局**: 7x7网格
**新增元素**: 
- 时间限制: 3分钟
- 每30秒污染值+5

**废料配置**:
- 基础废料: 70%
- 时限废料: 30% (橙色，60秒后变成污染核心)

**策略要点**:
- 优先清除时限废料
- 合理规划消除顺序
- 利用连锁反应提高效率

### 2.2 进阶教学关卡

#### 关卡4: 防御初体验
**学习目标**: 基础塔防机制
**地图布局**: 8x8网格，中央有净化装置
**新增元素**:
- 第一次敌人入侵 (3只污染爬虫)
- 可建造1个风力过滤器

**敌人路径**:
```
入口→→→→→→→净化装置
```

**建造指引**:
1. 消除15个废料后触发入侵警告
2. 引导玩家在路径旁建造风力过滤器
3. 展示减速效果和污染清除效果

#### 关卡5: 资源管理
**学习目标**: 资源收集和建造成本
**地图布局**: 8x8网格，L型地形
**建造选择**:
- 风力过滤器 (金属x3, 塑料x2)
- 声波驱离器 (电子x2, 金属x1)

**策略挑战**:
- 资源有限，需要选择合适的防御设施
- 两波敌人从不同方向入侵
- 需要合理分配资源

#### 关卡6-10: 综合应用
**关卡6**: 多路径防御 - 敌人从3个方向同时入侵
**关卡7**: 特殊废料挑战 - 引入冰冻废料和变异废料
**关卡8**: 完美净化 - 不允许净化装置受到任何伤害
**关卡9**: 资源竞速 - 在限定时间内收集指定资源
**关卡10**: 章节Boss - 对抗第一个污染之王

## 3. 第二章：工业遗迹 (关卡11-25)

### 3.1 工业主题设计

#### 环境设定
- **视觉风格**: 废弃的工厂和机械设备
- **色调**: 铁锈红+机械灰+污染绿
- **音效**: 机械运转声+蒸汽声+金属碰撞

#### 新增废料类型
**机械废料** (银色+齿轮图案):
- 消除时产生机械能量
- 3个相邻的机械废料可激活附近的废弃机械

**化学废料** (紫色+危险标志):
- 消除时产生化学反应
- 与其他废料接触会改变其属性

### 3.2 核心关卡设计

#### 关卡11: 流水线重启
**创新机制**: 传送带系统
**地图特色**: 
- 3条水平传送带，废料每回合向右移动1格
- 玩家需要在废料移出屏幕前消除

**布局示意**:
```
[←←←←←←←←] 传送带1
[废料区域]
[←←←←←←←←] 传送带2  
[废料区域]
[←←←←←←←←] 传送带3
```

**策略要点**:
- 优先消除即将移出的废料
- 利用传送带制造连锁反应
- 在传送带末端设置防御设施

#### 关卡15: 化学反应炉
**创新机制**: 元素反应系统
**反应规则**:
- 金属+化学 = 产生腐蚀效果，清除周围污染
- 塑料+化学 = 产生聚合反应，生成高价值材料
- 有机+化学 = 产生发酵反应，范围净化

**目标**: 触发至少5次不同的化学反应

#### 关卡20: 工厂保卫战
**大型塔防关卡**:
- 3波大规模敌人入侵
- 可建造所有类型的防御设施
- 引入新敌人：机械蜘蛛（免疫减速，但怕电磁脉冲）

## 4. 第三章：核心污染区 (关卡26-40)

### 4.1 核污染主题

#### 环境设定
- **视觉风格**: 核电站废墟，绿色辐射光
- **新增元素**: 辐射区域，玩家无法在此建造
- **特殊效果**: 辐射会让废料发生变异

#### 新增机制
**辐射系统**:
- 辐射区域每回合增加污染值
- 某些废料在辐射中会变异成更危险的形态
- 需要特殊的净化设施来清除辐射

### 4.2 高难度关卡设计

#### 关卡30: 反应堆核心
**终极挑战关卡**:
- 9x9网格，中央是高辐射区
- 辐射每回合向外扩散1格
- 必须在辐射扩散到边缘前完成净化

**创新玩法**:
- 引入"辐射防护服"道具，可临时进入辐射区
- 核心区域有大量再生核心，但风险极高

#### 关卡35: 连锁反应
**机制设计**:
- 地图上有多个"核燃料棒"
- 消除核燃料棒会引发连锁爆炸
- 需要精确计算爆炸顺序，避免过度污染

## 5. 特殊关卡类型设计

### 5.1 Boss关卡设计

#### 污染之王战斗机制
**阶段1**: 普通移动，生命值100
- 每受到25点伤害进入下一阶段
- 移动速度逐渐加快

**阶段2**: 召唤小怪
- 每3回合召唤2-3只污染爬虫
- 本体移动速度减半

**阶段3**: 狂暴模式
- 移动速度翻倍
- 每回合对周围造成污染伤害
- 免疫所有减速效果

**阶段4**: 最终形态
- 分裂成3个小Boss
- 必须同时击败才能获胜

### 5.2 解谜关卡设计

#### 关卡类型：废料拼图
**机制**:
- 地图上的废料形成特定图案
- 玩家需要通过消除重新排列废料
- 形成指定图案后获得大量奖励

**示例图案**:
- 和平鸽：象征希望与重生
- 绿叶：代表环保主题
- 齿轮：工业文明的重建

### 5.3 限时挑战关卡

#### 每日挑战设计
**周一：速度挑战** - 在最短时间内完成净化
**周二：效率挑战** - 用最少步数完成目标
**周三：防御挑战** - 抵御超大规模敌人入侵
**周四：收集挑战** - 收集指定数量的稀有资源
**周五：完美挑战** - 零伤害完成高难度关卡
**周六：创意挑战** - 使用特定消除技巧
**周日：综合挑战** - 随机组合多种挑战条件

## 6. 关卡编辑器设计

### 6.1 编辑器功能
**基础功能**:
- 拖拽放置废料块
- 设置敌人路径和刷新点
- 调整地形和障碍物
- 配置目标条件和限制

**高级功能**:
- 脚本事件系统
- 自定义敌人AI
- 动态环境变化
- 多阶段关卡设计

### 6.2 UGC内容支持
**分享机制**:
- 关卡代码分享
- 在线关卡库
- 评分和评论系统
- 优秀作品推荐

**激励系统**:
- 创作者奖励计划
- 热门关卡制作者认证
- 官方关卡征集活动

## 7. 关卡数据结构

### 7.1 关卡配置文件格式
```json
{
  "levelId": 1,
  "chapterName": "废墟觉醒",
  "levelName": "初次觉醒",
  "difficulty": 1,
  "gridSize": {"width": 6, "height": 6},
  "initialPollution": 50,
  "targetPollution": 10,
  "stepLimit": 50,
  "timeLimit": null,
  "wasteBlocks": [
    {"type": "metal", "position": [0,0], "count": 1},
    {"type": "plastic", "position": [0,1], "count": 1}
  ],
  "enemies": [],
  "specialEvents": [],
  "rewards": {
    "basicReward": {"metal": 10, "plastic": 5},
    "starRewards": [
      {"stars": 1, "condition": "complete", "reward": {"cores": 1}},
      {"stars": 2, "condition": "steps<40", "reward": {"cores": 2}},
      {"stars": 3, "condition": "perfect", "reward": {"cores": 3}}
    ]
  }
}
```

### 7.2 动态难度调整
**自适应系统**:
- 根据玩家历史表现调整难度
- 失败次数过多时提供提示或降低难度
- 连续成功时适当增加挑战

**个性化推荐**:
- 分析玩家偏好的关卡类型
- 推荐相似风格的用户创作关卡
- 根据游戏时间推荐合适难度的内容

---

*关卡设计是游戏体验的核心，每个关卡都应该有明确的教学目标和独特的挑战。通过精心设计的难度曲线和多样化的玩法机制，确保玩家在整个游戏过程中都能获得持续的成就感和新鲜感。*
