using System.Collections;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 干净简洁的游戏启动器 - 专为初学者设计
    /// 只显示必要的信息，避免界面混乱
    /// </summary>
    public class CleanGameStarter : MonoBehaviour
    {
        [Header("启动设置")]
        public bool autoStart = true;
        public bool showSimpleUI = true;
        public bool enableDebugMode = false;

        [Header("游戏状态")]
        public bool isGameReady = false;

        private void Start()
        {
            if (autoStart)
            {
                StartCoroutine(InitializeCleanGame());
            }
        }

        private IEnumerator InitializeCleanGame()
        {
            Debug.Log("=== 开始初始化游戏 ===");

            // 1. 设置相机
            SetupCamera();
            yield return new WaitForSeconds(0.2f);

            // 2. 创建GameManager（但不显示复杂UI）
            CreateSimpleGameManager();
            yield return new WaitForSeconds(0.2f);

            // 3. 创建基础网格系统
            CreateGridSystem();
            yield return new WaitForSeconds(0.2f);

            // 4. 创建地面
            CreateGround();
            yield return new WaitForSeconds(0.2f);

            isGameReady = true;
            Debug.Log("=== 游戏初始化完成！===");
            Debug.Log("现在你可以：");
            Debug.Log("- 按 1 键创建测试关卡");
            Debug.Log("- 按 2 键创建一些废料块");
            Debug.Log("- 按 3 键清理场景");
            Debug.Log("- 点击废料块进行消除");
        }

        private void SetupCamera()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
            {
                GameObject cameraObj = new GameObject("Main Camera");
                mainCamera = cameraObj.AddComponent<Camera>();
                cameraObj.tag = "MainCamera";
            }

            // 设置一个更好的游戏视角
            mainCamera.transform.position = new Vector3(4, 8, -6);
            mainCamera.transform.eulerAngles = new Vector3(35, 0, 0);
            mainCamera.clearFlags = CameraClearFlags.SolidColor;
            mainCamera.backgroundColor = new Color(0.3f, 0.5f, 0.7f); // 天空蓝

            // 添加简单的相机控制
            if (mainCamera.GetComponent<SimpleCameraControl>() == null)
            {
                mainCamera.gameObject.AddComponent<SimpleCameraControl>();
            }

            Debug.Log("✓ 相机设置完成");
        }

        private void CreateSimpleGameManager()
        {
            if (GameManager.Instance == null)
            {
                GameObject gameManagerObj = new GameObject("GameManager");
                gameManagerObj.AddComponent<GameManager>();
                Debug.Log("✓ GameManager 创建完成");
            }
        }

        private void CreateGridSystem()
        {
            if (FindObjectOfType<GridSystem>() == null)
            {
                GameObject gridSystemObj = new GameObject("GridSystem");
                GridSystem gridSystem = gridSystemObj.AddComponent<GridSystem>();
                
                // 设置网格参数
                gridSystem.gridWidth = 8;
                gridSystem.gridHeight = 8;
                gridSystem.cellSize = 1.0f;
                gridSystem.gridOrigin = Vector3.zero;

                Debug.Log("✓ 网格系统创建完成");
            }
        }

        private void CreateGround()
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.position = new Vector3(3.5f, -0.5f, 3.5f);
            ground.transform.localScale = new Vector3(1.2f, 1, 1.2f);

            // 设置地面材质
            Renderer groundRenderer = ground.GetComponent<Renderer>();
            Material groundMaterial = new Material(Shader.Find("Standard"));
            groundMaterial.color = new Color(0.4f, 0.3f, 0.2f); // 废土棕色
            groundRenderer.material = groundMaterial;

            Debug.Log("✓ 地面创建完成");
        }

        private void Update()
        {
            if (!isGameReady) return;

            HandleInput();
        }

        private void HandleInput()
        {
            // 1键 - 创建测试关卡
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                CreateTestLevel();
            }

            // 2键 - 创建废料块
            if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                CreateWasteBlocks();
            }

            // 3键 - 清理场景
            if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                ClearScene();
            }

            // T键 - 切换调试模式
            if (Input.GetKeyDown(KeyCode.T))
            {
                enableDebugMode = !enableDebugMode;
                Debug.Log($"调试模式: {(enableDebugMode ? "开启" : "关闭")}");
            }
        }

        private void CreateTestLevel()
        {
            Debug.Log("创建测试关卡...");

            GridSystem gridSystem = FindObjectOfType<GridSystem>();
            if (gridSystem != null)
            {
                // 初始化网格系统
                gridSystem.Initialize();

                // 创建简单的测试关卡数据
                LevelData testLevel = new LevelData
                {
                    levelId = 1,
                    levelName = "测试关卡",
                    gridSize = new Vector2Int(8, 8),
                    initialPollution = 50,
                    targetPollution = 10,
                    stepLimit = 30,
                    purifierPosition = new Vector2Int(7, 0)
                };

                // 添加一些废料块
                for (int i = 0; i < 12; i++)
                {
                    testLevel.wasteBlocks.Add(new WasteBlockData
                    {
                        type = (WasteBlockType)(i % 6),
                        position = new Vector2Int(Random.Range(0, 6), Random.Range(0, 8)),
                        pollutionValue = 2
                    });
                }

                // 启动关卡
                if (GameManager.Instance != null)
                {
                    GameManager.Instance.StartLevel(testLevel);
                }

                Debug.Log("✓ 测试关卡创建完成！点击彩色方块进行消除");
            }
        }

        private void CreateWasteBlocks()
        {
            Debug.Log("创建废料块...");

            // 创建一些简单的废料块用于测试
            Color[] colors = {
                Color.red, Color.blue, Color.green, 
                Color.yellow, Color.magenta, Color.cyan
            };

            for (int i = 0; i < 5; i++)
            {
                GameObject wasteBlock = GameObject.CreatePrimitive(PrimitiveType.Cube);
                wasteBlock.name = $"WasteBlock_{i}";
                wasteBlock.transform.position = new Vector3(
                    Random.Range(0, 8), 
                    0.5f, 
                    Random.Range(0, 8)
                );
                wasteBlock.transform.localScale = new Vector3(0.8f, 0.8f, 0.8f);

                // 设置颜色
                Renderer renderer = wasteBlock.GetComponent<Renderer>();
                Material material = new Material(Shader.Find("Standard"));
                material.color = colors[i % colors.Length];
                renderer.material = material;

                // 添加点击检测
                wasteBlock.AddComponent<SimpleClickHandler>();
            }

            Debug.Log("✓ 废料块创建完成！点击它们试试");
        }

        private void ClearScene()
        {
            Debug.Log("清理场景...");

            // 清理所有废料块
            GameObject[] wasteBlocks = GameObject.FindGameObjectsWithTag("Untagged");
            foreach (GameObject obj in wasteBlocks)
            {
                if (obj.name.Contains("WasteBlock") || obj.name.Contains("Cube"))
                {
                    Destroy(obj);
                }
            }

            Debug.Log("✓ 场景清理完成");
        }

        private void OnGUI()
        {
            if (!showSimpleUI || !isGameReady) return;

            // 显示简洁的操作界面
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.BeginVertical("box");

            GUILayout.Label("废土再生：回收者大作战", new GUIStyle(GUI.skin.label) 
            { 
                fontSize = 14, 
                fontStyle = FontStyle.Bold 
            });

            GUILayout.Space(10);

            GUILayout.Label("操作指南：");
            GUILayout.Label("1 - 创建测试关卡");
            GUILayout.Label("2 - 创建废料块");
            GUILayout.Label("3 - 清理场景");
            GUILayout.Label("T - 切换调试模式");

            GUILayout.Space(10);

            if (GUILayout.Button("创建测试关卡"))
            {
                CreateTestLevel();
            }

            if (GUILayout.Button("创建废料块"))
            {
                CreateWasteBlocks();
            }

            if (GUILayout.Button("清理场景"))
            {
                ClearScene();
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();

            // 如果开启调试模式，显示额外信息
            if (enableDebugMode)
            {
                GUILayout.BeginArea(new Rect(Screen.width - 200, 10, 190, 150));
                GUILayout.BeginVertical("box");

                GUILayout.Label("调试信息", new GUIStyle(GUI.skin.label) { fontStyle = FontStyle.Bold });
                GUILayout.Label($"FPS: {(int)(1.0f / Time.deltaTime)}");
                GUILayout.Label($"对象数: {FindObjectsOfType<GameObject>().Length}");
                
                if (GameManager.Instance != null)
                {
                    GUILayout.Label($"游戏状态: {GameManager.Instance.currentState}");
                }

                GUILayout.EndVertical();
                GUILayout.EndArea();
            }
        }
    }

    /// <summary>
    /// 简单的相机控制
    /// </summary>
    public class SimpleCameraControl : MonoBehaviour
    {
        public float rotateSpeed = 50f;
        public float zoomSpeed = 2f;

        private void Update()
        {
            // 右键拖拽旋转
            if (Input.GetMouseButton(1))
            {
                float mouseX = Input.GetAxis("Mouse X");
                transform.Rotate(Vector3.up, mouseX * rotateSpeed * Time.deltaTime, Space.World);
            }

            // 滚轮缩放
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (scroll != 0f)
            {
                transform.Translate(Vector3.forward * scroll * zoomSpeed);
            }
        }
    }

    /// <summary>
    /// 简单的点击处理器
    /// </summary>
    public class SimpleClickHandler : MonoBehaviour
    {
        private void OnMouseDown()
        {
            Debug.Log($"点击了 {gameObject.name}");
            
            // 简单的消除效果
            StartCoroutine(DestroyEffect());
        }

        private System.Collections.IEnumerator DestroyEffect()
        {
            // 缩放动画
            Vector3 originalScale = transform.localScale;
            float duration = 0.3f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float scale = Mathf.Lerp(1f, 0f, elapsed / duration);
                transform.localScale = originalScale * scale;
                yield return null;
            }

            Destroy(gameObject);
            Debug.Log("废料块已消除！");
        }
    }
}
