using UnityEngine;

namespace WastelandReclaim
{
    public class GridCell : MonoBehaviour
    {
        [Header("Cell Properties")]
        public Vector2Int gridPosition;
        public bool IsBlocked { get; private set; } = false;
        public bool IsOccupied { get; private set; } = false;

        [Header("Visual")]
        public Renderer cellRenderer;
        public GameObject blockedIndicator;
        public GameObject hoverHighlight;
        public GameObject selectionHighlight;

        [Header("Colors")]
        public Color normalColor = Color.white;
        public Color hoveredColor = Color.yellow;
        public Color selectedColor = Color.green;
        public Color blockedColor = Color.red;
        public Color buildableColor = Color.blue;

        private GridSystem gridSystem;
        private bool isHovered = false;
        private bool isSelected = false;
        private bool isBuildMode = false;

        public void Initialize(Vector2Int position, GridSystem grid)
        {
            gridPosition = position;
            gridSystem = grid;
            
            SetupVisuals();
            UpdateVisuals();
        }

        private void SetupVisuals()
        {
            if (cellRenderer == null)
                cellRenderer = GetComponent<Renderer>();

            if (hoverHighlight != null)
                hoverHighlight.SetActive(false);

            if (selectionHighlight != null)
                selectionHighlight.SetActive(false);

            if (blockedIndicator != null)
                blockedIndicator.SetActive(false);
        }

        private void UpdateVisuals()
        {
            if (cellRenderer == null) return;

            Color targetColor = normalColor;

            if (IsBlocked)
            {
                targetColor = blockedColor;
            }
            else if (isSelected)
            {
                targetColor = selectedColor;
            }
            else if (isHovered)
            {
                if (isBuildMode && CanBuildHere())
                {
                    targetColor = buildableColor;
                }
                else
                {
                    targetColor = hoveredColor;
                }
            }

            cellRenderer.material.color = targetColor;

            // Update highlight objects
            if (hoverHighlight != null)
                hoverHighlight.SetActive(isHovered && !IsBlocked);

            if (selectionHighlight != null)
                selectionHighlight.SetActive(isSelected);

            if (blockedIndicator != null)
                blockedIndicator.SetActive(IsBlocked);
        }

        public void SetBlocked(bool blocked)
        {
            IsBlocked = blocked;
            UpdateVisuals();
        }

        public void SetOccupied(bool occupied)
        {
            IsOccupied = occupied;
            UpdateVisuals();
        }

        public void SetSelected(bool selected)
        {
            isSelected = selected;
            UpdateVisuals();
        }

        public void SetHovered(bool hovered)
        {
            isHovered = hovered;
            UpdateVisuals();
        }

        public void SetBuildMode(bool buildMode)
        {
            isBuildMode = buildMode;
            UpdateVisuals();
        }

        public bool CanBuildHere()
        {
            return !IsBlocked && !IsOccupied && gridSystem.GetWasteBlock(gridPosition) == null;
        }

        private void OnMouseEnter()
        {
            SetHovered(true);
        }

        private void OnMouseExit()
        {
            SetHovered(false);
        }

        private void OnMouseDown()
        {
            if (GameManager.Instance == null || GameManager.Instance.currentState != GameState.Playing)
                return;

            if (gridSystem != null)
            {
                gridSystem.OnCellClicked(gridPosition);
            }
        }

        private void OnMouseOver()
        {
            // Show tooltip or additional information when hovering
            if (isHovered)
            {
                ShowTooltip();
            }
        }

        private void ShowTooltip()
        {
            // This would show a tooltip with cell information
            // For now, just log the information
            string tooltip = $"Position: {gridPosition}";
            
            if (IsBlocked)
            {
                tooltip += "\nBlocked - Cannot build here";
            }
            else if (IsOccupied)
            {
                tooltip += "\nOccupied";
            }
            else if (gridSystem.GetWasteBlock(gridPosition) != null)
            {
                WasteBlock wasteBlock = gridSystem.GetWasteBlock(gridPosition);
                tooltip += $"\n{wasteBlock.GetBlockDescription()}";
            }
            else if (CanBuildHere())
            {
                tooltip += "\nEmpty - Can build tower here";
            }

            // In a real implementation, this would update a UI tooltip
            Debug.Log(tooltip);
        }

        public Vector3 GetWorldPosition()
        {
            return transform.position;
        }

        public void PlayClickEffect()
        {
            // Play a visual or audio effect when clicked
            StartCoroutine(ClickEffectCoroutine());
        }

        private System.Collections.IEnumerator ClickEffectCoroutine()
        {
            Vector3 originalScale = transform.localScale;
            Vector3 targetScale = originalScale * 1.1f;

            // Scale up
            float duration = 0.1f;
            float elapsed = 0f;

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                transform.localScale = Vector3.Lerp(originalScale, targetScale, t);
                yield return null;
            }

            // Scale back down
            elapsed = 0f;
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                transform.localScale = Vector3.Lerp(targetScale, originalScale, t);
                yield return null;
            }

            transform.localScale = originalScale;
        }

        public void ShowBuildPreview(TowerType towerType)
        {
            // Show a preview of what tower would be built here
            // This would instantiate a semi-transparent preview object
            Debug.Log($"Showing build preview for {towerType} at {gridPosition}");
        }

        public void HideBuildPreview()
        {
            // Hide the build preview
            Debug.Log($"Hiding build preview at {gridPosition}");
        }

        // Utility methods for pathfinding and AI
        public float GetMovementCost()
        {
            if (IsBlocked) return float.MaxValue;
            if (IsOccupied) return 2f; // Higher cost but still passable
            return 1f; // Normal movement cost
        }

        public bool IsPassable()
        {
            return !IsBlocked;
        }

        public Vector2Int[] GetNeighborPositions()
        {
            return new Vector2Int[]
            {
                gridPosition + Vector2Int.up,
                gridPosition + Vector2Int.down,
                gridPosition + Vector2Int.left,
                gridPosition + Vector2Int.right
            };
        }
    }
}
