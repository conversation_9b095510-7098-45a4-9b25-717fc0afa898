using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    /// <summary>
    /// 快速启动脚本 - 专为Unity 2021.3优化
    /// 将此脚本添加到场景中的空GameObject上即可快速开始游戏
    /// </summary>
    public class QuickStart : MonoBehaviour
    {
        [Header("快速设置")]
        public bool autoStart = true;
        public bool createBasicPrefabs = true;
        public bool showInstructions = true;

        [Header("基础设置")]
        public Material defaultMaterial;
        public Material gridMaterial;

        private void Start()
        {
            if (autoStart)
            {
                StartCoroutine(QuickStartGame());
            }
        }

        private IEnumerator QuickStartGame()
        {
            Debug.Log("=== 快速启动游戏 ===");

            // 1. 创建基础材质
            CreateBasicMaterials();
            yield return null;

            // 2. 设置相机
            SetupCamera();
            yield return null;

            // 3. 创建GameManager
            CreateGameManager();
            yield return null;

            // 4. 创建其他系统
            CreateGameSystems();
            yield return null;

            // 5. 创建基础预制体
            if (createBasicPrefabs)
            {
                CreateBasicPrefabs();
                yield return null;
            }

            // 6. 启动游戏
            StartGame();

            Debug.Log("=== 游戏启动完成！按T键开始测试 ===");
        }

        public void CreateBasicMaterials()
        {
            // 创建默认材质
            if (defaultMaterial == null)
            {
                defaultMaterial = new Material(Shader.Find("Standard"));
                defaultMaterial.color = Color.white;
            }

            // 创建网格材质
            if (gridMaterial == null)
            {
                gridMaterial = new Material(Shader.Find("Standard"));
                gridMaterial.color = new Color(1f, 1f, 1f, 0.3f);
                gridMaterial.SetFloat("_Mode", 3); // Transparent mode
                gridMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                gridMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                gridMaterial.SetInt("_ZWrite", 0);
                gridMaterial.DisableKeyword("_ALPHATEST_ON");
                gridMaterial.EnableKeyword("_ALPHABLEND_ON");
                gridMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
                gridMaterial.renderQueue = 3000;
            }
        }

        public void SetupCamera()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
            {
                GameObject cameraObj = new GameObject("Main Camera");
                mainCamera = cameraObj.AddComponent<Camera>();
                cameraObj.tag = "MainCamera";
            }

            mainCamera.transform.position = new Vector3(4, 10, -8);
            mainCamera.transform.eulerAngles = new Vector3(45, 0, 0);
            mainCamera.clearFlags = CameraClearFlags.SolidColor;
            mainCamera.backgroundColor = new Color(0.2f, 0.3f, 0.4f);

            // 添加简单的相机控制
            SimpleCameraController cameraController = mainCamera.GetComponent<SimpleCameraController>();
            if (cameraController == null)
            {
                cameraController = mainCamera.gameObject.AddComponent<SimpleCameraController>();
            }
        }

        public void CreateGameManager()
        {
            if (GameManager.Instance == null)
            {
                GameObject gameManagerObj = new GameObject("GameManager");
                gameManagerObj.AddComponent<GameManager>();
                DontDestroyOnLoad(gameManagerObj);
            }
        }

        public void CreateGameSystems()
        {
            // 创建GridSystem
            GameObject gridSystemObj = new GameObject("GridSystem");
            GridSystem gridSystem = gridSystemObj.AddComponent<GridSystem>();
            
            // 设置GridSystem的基础属性
            gridSystem.gridWidth = 8;
            gridSystem.gridHeight = 8;
            gridSystem.cellSize = 1.0f;
            gridSystem.gridOrigin = Vector3.zero;
            gridSystem.gridMaterial = gridMaterial;

            // 创建其他系统
            GameObject matchSystemObj = new GameObject("MatchSystem");
            matchSystemObj.AddComponent<MatchSystem>();

            GameObject towerSystemObj = new GameObject("TowerSystem");
            towerSystemObj.AddComponent<TowerSystem>();

            GameObject enemySystemObj = new GameObject("EnemySystem");
            enemySystemObj.AddComponent<EnemySystem>();

            GameObject uiManagerObj = new GameObject("UIManager");
            uiManagerObj.AddComponent<UIManager>();

            GameObject audioManagerObj = new GameObject("AudioManager");
            audioManagerObj.AddComponent<AudioManager>();

            // 添加测试器
            GameObject testerObj = new GameObject("GameTester");
            testerObj.AddComponent<GameTester>();
        }

        public void CreateBasicPrefabs()
        {
            // 创建网格单元预制体
            CreateGridCellPrefab();

            // 创建废料块预制体
            CreateWasteBlockPrefabs();

            // 创建防御塔预制体
            CreateTowerPrefabs();

            // 创建敌人预制体
            CreateEnemyPrefabs();
        }

        private void CreateGridCellPrefab()
        {
            GameObject cellPrefab = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cellPrefab.name = "GridCell";
            cellPrefab.transform.localScale = new Vector3(1f, 0.1f, 1f);
            
            // 设置材质
            Renderer renderer = cellPrefab.GetComponent<Renderer>();
            renderer.material = gridMaterial;

            // 添加GridCell组件
            cellPrefab.AddComponent<GridCell>();

            // 保存为预制体（在运行时创建的简化版本）
            GridSystem gridSystem = FindObjectOfType<GridSystem>();
            if (gridSystem != null)
            {
                gridSystem.cellPrefab = cellPrefab;
            }
        }

        private void CreateWasteBlockPrefabs()
        {
            GridSystem gridSystem = FindObjectOfType<GridSystem>();
            if (gridSystem == null) return;

            // 创建废料块预制体数组
            gridSystem.wasteBlockPrefabs = new GameObject[6];

            Color[] wasteColors = {
                new Color(0.7f, 0.7f, 0.8f), // Metal - Silver
                new Color(0.2f, 0.6f, 1f),   // Plastic - Blue
                new Color(0.6f, 0.4f, 0.2f), // Wood - Brown
                new Color(0.2f, 0.8f, 0.2f), // Electronic - Green
                new Color(0.9f, 0.9f, 0.9f), // Glass - White
                new Color(1f, 0.8f, 0.2f)    // Organic - Yellow
            };

            for (int i = 0; i < 6; i++)
            {
                GameObject wasteBlock = GameObject.CreatePrimitive(PrimitiveType.Cube);
                wasteBlock.name = $"WasteBlock_{(WasteBlockType)i}";
                wasteBlock.transform.localScale = new Vector3(0.8f, 0.8f, 0.8f);

                // 设置颜色
                Material wasteMaterial = new Material(Shader.Find("Standard"));
                wasteMaterial.color = wasteColors[i];
                wasteBlock.GetComponent<Renderer>().material = wasteMaterial;

                // 添加WasteBlock组件
                wasteBlock.AddComponent<WasteBlock>();

                gridSystem.wasteBlockPrefabs[i] = wasteBlock;
            }
        }

        private void CreateTowerPrefabs()
        {
            TowerSystem towerSystem = FindObjectOfType<TowerSystem>();
            if (towerSystem == null) return;

            towerSystem.towerPrefabs = new GameObject[6];

            Color[] towerColors = {
                new Color(0.7f, 0.9f, 1f),   // WindFilter - Light blue
                new Color(1f, 0.8f, 0.2f),   // SonicRepeller - Orange
                new Color(0.2f, 0.8f, 0.2f), // ShieldGenerator - Green
                new Color(0.8f, 0.2f, 0.8f), // ResourceAccelerator - Purple
                new Color(1f, 0.2f, 0.2f),   // LaserTurret - Red
                new Color(0.2f, 0.2f, 1f)    // ElectromagneticNet - Blue
            };

            for (int i = 0; i < 6; i++)
            {
                GameObject tower = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
                tower.name = $"Tower_{(TowerType)i}";
                tower.transform.localScale = new Vector3(0.6f, 1f, 0.6f);

                // 设置颜色
                Material towerMaterial = new Material(Shader.Find("Standard"));
                towerMaterial.color = towerColors[i];
                tower.GetComponent<Renderer>().material = towerMaterial;

                // 添加Tower组件
                tower.AddComponent<Tower>();

                // 创建范围指示器
                GameObject rangeIndicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                rangeIndicator.name = "RangeIndicator";
                rangeIndicator.transform.SetParent(tower.transform);
                rangeIndicator.transform.localPosition = Vector3.zero;
                rangeIndicator.transform.localScale = Vector3.one * 6f; // 3格范围

                // 设置范围指示器材质
                Material rangeMaterial = new Material(Shader.Find("Standard"));
                rangeMaterial.color = new Color(towerColors[i].r, towerColors[i].g, towerColors[i].b, 0.2f);
                rangeMaterial.SetFloat("_Mode", 3); // Transparent
                rangeMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                rangeMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                rangeMaterial.SetInt("_ZWrite", 0);
                rangeMaterial.EnableKeyword("_ALPHABLEND_ON");
                rangeMaterial.renderQueue = 3000;
                rangeIndicator.GetComponent<Renderer>().material = rangeMaterial;

                // 移除碰撞器
                Destroy(rangeIndicator.GetComponent<Collider>());
                rangeIndicator.SetActive(false);

                // 设置Tower组件的rangeIndicator引用
                Tower towerComponent = tower.GetComponent<Tower>();
                towerComponent.rangeIndicator = rangeIndicator;

                towerSystem.towerPrefabs[i] = tower;
            }
        }

        private void CreateEnemyPrefabs()
        {
            EnemySystem enemySystem = FindObjectOfType<EnemySystem>();
            if (enemySystem == null) return;

            enemySystem.enemyPrefabs = new GameObject[4];

            Color[] enemyColors = {
                new Color(0.6f, 0.4f, 0.2f), // PollutionCrawler - Brown
                new Color(0.8f, 0.8f, 0.2f), // CorrosiveFly - Yellow-green
                new Color(0.4f, 0.2f, 0.6f), // MutatedPlant - Purple
                new Color(0.2f, 0.1f, 0.1f)  // PollutionKing - Dark red
            };

            for (int i = 0; i < 4; i++)
            {
                GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                enemy.name = $"Enemy_{(EnemyType)i}";
                enemy.transform.localScale = new Vector3(0.5f, 0.5f, 0.5f);

                // 设置颜色
                Material enemyMaterial = new Material(Shader.Find("Standard"));
                enemyMaterial.color = enemyColors[i];
                enemy.GetComponent<Renderer>().material = enemyMaterial;

                // 添加Enemy组件
                enemy.AddComponent<Enemy>();

                // 创建血条
                GameObject healthBar = GameObject.CreatePrimitive(PrimitiveType.Cube);
                healthBar.name = "HealthBar";
                healthBar.transform.SetParent(enemy.transform);
                healthBar.transform.localPosition = new Vector3(0, 1.2f, 0);
                healthBar.transform.localScale = new Vector3(1f, 0.1f, 0.1f);

                Material healthMaterial = new Material(Shader.Find("Standard"));
                healthMaterial.color = Color.green;
                healthBar.GetComponent<Renderer>().material = healthMaterial;

                // 移除血条碰撞器
                Destroy(healthBar.GetComponent<Collider>());

                // 设置Enemy组件的healthBar引用
                Enemy enemyComponent = enemy.GetComponent<Enemy>();
                enemyComponent.healthBar = healthBar;

                enemySystem.enemyPrefabs[i] = enemy;
            }
        }

        public void StartGame()
        {
            // 连接所有系统
            if (GameManager.Instance != null)
            {
                GameManager.Instance.gridSystem = FindObjectOfType<GridSystem>();
                GameManager.Instance.matchSystem = FindObjectOfType<MatchSystem>();
                GameManager.Instance.towerSystem = FindObjectOfType<TowerSystem>();
                GameManager.Instance.enemySystem = FindObjectOfType<EnemySystem>();
                GameManager.Instance.uiManager = FindObjectOfType<UIManager>();
                GameManager.Instance.audioManager = FindObjectOfType<AudioManager>();

                // 初始化所有系统
                GameManager.Instance.gridSystem?.Initialize();
                GameManager.Instance.matchSystem?.Initialize();
                GameManager.Instance.towerSystem?.Initialize();
                GameManager.Instance.enemySystem?.Initialize();
                GameManager.Instance.uiManager?.Initialize();
                GameManager.Instance.audioManager?.Initialize();

                // 设置初始状态
                GameManager.Instance.ChangeGameState(GameState.MainMenu);
            }
        }

        private void OnGUI()
        {
            if (showInstructions)
            {
                GUILayout.BeginArea(new Rect(10, 10, 400, 300));
                GUILayout.BeginVertical("box");

                GUILayout.Label("=== 废土再生：回收者大作战 ===", new GUIStyle(GUI.skin.label) { fontSize = 16, fontStyle = FontStyle.Bold });
                GUILayout.Space(10);

                GUILayout.Label("快速开始指南：");
                GUILayout.Label("1. 按 T 键 - 运行所有系统测试");
                GUILayout.Label("2. 按 1 键 - 开始测试关卡");
                GUILayout.Label("3. 点击废料块进行消除");
                GUILayout.Label("4. 按 2 键 - 测试建造防御塔");
                GUILayout.Label("5. 按 3 键 - 测试敌人系统");

                GUILayout.Space(10);
                GUILayout.Label("游戏目标：");
                GUILayout.Label("• 消除废料块获得资源");
                GUILayout.Label("• 建造防御塔抵御敌人");
                GUILayout.Label("• 将污染值降到目标以下");

                if (GUILayout.Button("开始测试关卡"))
                {
                    GameTester tester = FindObjectOfType<GameTester>();
                    if (tester != null)
                    {
                        tester.StartCoroutine("TestBasicGameplay");
                    }
                }

                GUILayout.EndVertical();
                GUILayout.EndArea();
            }
        }
    }

    /// <summary>
    /// 简单的相机控制器
    /// </summary>
    public class SimpleCameraController : MonoBehaviour
    {
        public float mouseSensitivity = 2f;
        public float zoomSpeed = 2f;
        public float minZoom = 5f;
        public float maxZoom = 20f;

        private Vector3 lastMousePosition;

        private void Update()
        {
            HandleInput();
        }

        private void HandleInput()
        {
            // 右键拖拽旋转
            if (Input.GetMouseButtonDown(1))
            {
                lastMousePosition = Input.mousePosition;
            }

            if (Input.GetMouseButton(1))
            {
                Vector3 deltaMousePosition = Input.mousePosition - lastMousePosition;
                transform.Rotate(Vector3.up, deltaMousePosition.x * mouseSensitivity, Space.World);
                transform.Rotate(Vector3.right, -deltaMousePosition.y * mouseSensitivity, Space.Self);
                lastMousePosition = Input.mousePosition;
            }

            // 滚轮缩放
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (scroll != 0f)
            {
                Vector3 position = transform.position;
                Vector3 forward = transform.forward;
                position += forward * scroll * zoomSpeed;

                float distance = Vector3.Distance(position, Vector3.zero);
                if (distance >= minZoom && distance <= maxZoom)
                {
                    transform.position = position;
                }
            }
        }
    }
}
