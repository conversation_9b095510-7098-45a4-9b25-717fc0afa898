using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    public class TowerSystem : MonoBehaviour
    {
        [Header("Tower Prefabs")]
        public GameObject[] towerPrefabs;

        [Header("Build Settings")]
        public LayerMask buildableLayer = 1;
        public Material previewMaterial;

        private Dictionary<Vector2Int, Tower> towers = new Dictionary<Vector2Int, Tower>();
        private GridSystem gridSystem;
        private TowerType selectedTowerType = TowerType.WindFilter;
        private bool isBuildMode = false;
        private GameObject buildPreview;
        private GameConfig gameConfig;

        public System.Action<TowerType> OnTowerTypeSelected;
        public System.Action<Vector2Int, TowerType> OnTowerBuilt;
        public System.Action<Vector2Int> OnTowerUpgraded;

        public void Initialize()
        {
            gridSystem = FindObjectOfType<GridSystem>();
            gameConfig = new GameConfig(); // In a real game, this would be loaded from data
            
            // Subscribe to events
            EventSystem.Subscribe<EnemySpawnEvent>(OnEnemySpawned);
        }

        private void Update()
        {
            if (isBuildMode)
            {
                UpdateBuildPreview();
                HandleBuildInput();
            }

            UpdateTowers();
        }

        public void SetBuildMode(bool buildMode, TowerType towerType = TowerType.WindFilter)
        {
            isBuildMode = buildMode;
            selectedTowerType = towerType;

            if (buildMode)
            {
                CreateBuildPreview();
            }
            else
            {
                DestroyBuildPreview();
            }

            OnTowerTypeSelected?.Invoke(towerType);
        }

        private void CreateBuildPreview()
        {
            if (buildPreview != null)
            {
                DestroyImmediate(buildPreview);
            }

            GameObject prefab = GetTowerPrefab(selectedTowerType);
            if (prefab != null)
            {
                buildPreview = Instantiate(prefab);
                
                // Make it semi-transparent
                Renderer[] renderers = buildPreview.GetComponentsInChildren<Renderer>();
                foreach (var renderer in renderers)
                {
                    Material[] materials = renderer.materials;
                    for (int i = 0; i < materials.Length; i++)
                    {
                        materials[i] = previewMaterial;
                    }
                    renderer.materials = materials;
                }

                // Disable colliders
                Collider[] colliders = buildPreview.GetComponentsInChildren<Collider>();
                foreach (var collider in colliders)
                {
                    collider.enabled = false;
                }

                buildPreview.SetActive(false);
            }
        }

        private void DestroyBuildPreview()
        {
            if (buildPreview != null)
            {
                DestroyImmediate(buildPreview);
                buildPreview = null;
            }
        }

        private void UpdateBuildPreview()
        {
            if (buildPreview == null) return;

            Vector3 mousePos = Input.mousePosition;
            Ray ray = Camera.main.ScreenPointToRay(mousePos);
            
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, buildableLayer))
            {
                Vector2Int gridPos = gridSystem.WorldToGridPosition(hit.point);
                
                if (CanBuildAt(gridPos))
                {
                    Vector3 worldPos = gridSystem.GridToWorldPosition(gridPos);
                    buildPreview.transform.position = worldPos;
                    buildPreview.SetActive(true);
                    
                    // Change color based on affordability
                    bool canAfford = CanAffordTower(selectedTowerType);
                    Color previewColor = canAfford ? Color.green : Color.red;
                    previewColor.a = 0.5f;
                    
                    Renderer[] renderers = buildPreview.GetComponentsInChildren<Renderer>();
                    foreach (var renderer in renderers)
                    {
                        renderer.material.color = previewColor;
                    }
                }
                else
                {
                    buildPreview.SetActive(false);
                }
            }
            else
            {
                buildPreview.SetActive(false);
            }
        }

        private void HandleBuildInput()
        {
            if (Input.GetMouseButtonDown(0))
            {
                Vector3 mousePos = Input.mousePosition;
                Ray ray = Camera.main.ScreenPointToRay(mousePos);
                
                if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, buildableLayer))
                {
                    Vector2Int gridPos = gridSystem.WorldToGridPosition(hit.point);
                    TryBuildTower(gridPos, selectedTowerType);
                }
            }

            if (Input.GetKeyDown(KeyCode.Escape))
            {
                SetBuildMode(false);
            }
        }

        public bool TryBuildTower(Vector2Int position, TowerType towerType)
        {
            if (!CanBuildAt(position))
            {
                Debug.Log("Cannot build tower at this position");
                return false;
            }

            if (!CanAffordTower(towerType))
            {
                Debug.Log("Cannot afford this tower");
                return false;
            }

            BuildTower(position, towerType);
            return true;
        }

        private bool CanBuildAt(Vector2Int position)
        {
            if (!gridSystem.IsValidPosition(position))
                return false;

            if (towers.ContainsKey(position))
                return false;

            if (gridSystem.GetWasteBlock(position) != null)
                return false;

            if (position == gridSystem.GetPurifierPosition())
                return false;

            return true;
        }

        private bool CanAffordTower(TowerType towerType)
        {
            if (!gameConfig.towerConfigs.ContainsKey(towerType))
                return false;

            var cost = gameConfig.towerConfigs[towerType].cost;
            return GameManager.Instance.CanAfford(cost);
        }

        private void BuildTower(Vector2Int position, TowerType towerType)
        {
            GameObject prefab = GetTowerPrefab(towerType);
            if (prefab == null) return;

            Vector3 worldPos = gridSystem.GridToWorldPosition(position);
            GameObject towerObj = Instantiate(prefab, worldPos, Quaternion.identity, transform);
            
            Tower tower = towerObj.GetComponent<Tower>();
            if (tower == null)
            {
                tower = towerObj.AddComponent<Tower>();
            }

            // Initialize tower
            var towerData = new TowerData
            {
                towerType = towerType,
                level = 1,
                position = position,
                stats = gameConfig.towerConfigs[towerType].baseStats
            };

            tower.Initialize(towerData);
            towers[position] = tower;

            // Spend resources
            var cost = gameConfig.towerConfigs[towerType].cost;
            GameManager.Instance.SpendResources(cost);

            // Publish event
            EventSystem.Publish(new TowerBuiltEvent
            {
                towerType = towerType,
                position = position,
                cost = cost
            });

            OnTowerBuilt?.Invoke(position, towerType);

            Debug.Log($"Built {towerType} tower at {position}");
        }

        private GameObject GetTowerPrefab(TowerType towerType)
        {
            int index = (int)towerType;
            if (index >= 0 && index < towerPrefabs.Length)
            {
                return towerPrefabs[index];
            }
            return null;
        }

        public bool TryUpgradeTower(Vector2Int position)
        {
            if (!towers.ContainsKey(position))
                return false;

            Tower tower = towers[position];
            if (tower.CanUpgrade())
            {
                var upgradeCost = tower.GetUpgradeCost();
                if (GameManager.Instance.CanAfford(upgradeCost))
                {
                    GameManager.Instance.SpendResources(upgradeCost);
                    tower.Upgrade();
                    
                    EventSystem.Publish(new TowerUpgradedEvent
                    {
                        tower = tower,
                        newLevel = tower.GetLevel()
                    });

                    OnTowerUpgraded?.Invoke(position);
                    return true;
                }
            }

            return false;
        }

        public void RemoveTower(Vector2Int position)
        {
            if (towers.ContainsKey(position))
            {
                Tower tower = towers[position];
                towers.Remove(position);
                
                if (tower != null && tower.gameObject != null)
                {
                    Destroy(tower.gameObject);
                }
            }
        }

        public Tower GetTower(Vector2Int position)
        {
            return towers.ContainsKey(position) ? towers[position] : null;
        }

        public List<Tower> GetAllTowers()
        {
            return new List<Tower>(towers.Values);
        }

        public List<Tower> GetTowersInRange(Vector2Int center, float range)
        {
            List<Tower> towersInRange = new List<Tower>();
            
            foreach (var kvp in towers)
            {
                float distance = Vector2Int.Distance(center, kvp.Key);
                if (distance <= range)
                {
                    towersInRange.Add(kvp.Value);
                }
            }
            
            return towersInRange;
        }

        private void UpdateTowers()
        {
            foreach (var tower in towers.Values)
            {
                if (tower != null)
                {
                    tower.UpdateTower();
                }
            }
        }

        public void ClearTowers()
        {
            foreach (var tower in towers.Values)
            {
                if (tower != null && tower.gameObject != null)
                {
                    DestroyImmediate(tower.gameObject);
                }
            }
            towers.Clear();
        }

        private void OnEnemySpawned(EnemySpawnEvent spawnEvent)
        {
            // Notify towers about new enemy
            foreach (var tower in towers.Values)
            {
                if (tower != null)
                {
                    tower.OnEnemySpawned(spawnEvent.enemy);
                }
            }
        }

        public Dictionary<ResourceType, int> GetTowerCost(TowerType towerType)
        {
            if (gameConfig.towerConfigs.ContainsKey(towerType))
            {
                return new Dictionary<ResourceType, int>(gameConfig.towerConfigs[towerType].cost);
            }
            return new Dictionary<ResourceType, int>();
        }

        public TowerStats GetTowerStats(TowerType towerType, int level = 1)
        {
            if (gameConfig.towerConfigs.ContainsKey(towerType))
            {
                var config = gameConfig.towerConfigs[towerType];
                if (level == 1)
                {
                    return config.baseStats;
                }
                else if (level - 2 < config.upgradeLevels.Count)
                {
                    return config.upgradeLevels[level - 2];
                }
            }
            return new TowerStats();
        }

        public string GetTowerDescription(TowerType towerType)
        {
            switch (towerType)
            {
                case TowerType.WindFilter:
                    return "风力过滤器 - 持续降低周围污染值";
                case TowerType.SonicRepeller:
                    return "声波驱离器 - 减缓敌人移动速度";
                case TowerType.ShieldGenerator:
                    return "护盾发生器 - 保护区域免受污染";
                case TowerType.ResourceAccelerator:
                    return "资源加速器 - 增加附近资源获取量";
                case TowerType.LaserTurret:
                    return "激光炮塔 - 直接攻击敌人";
                case TowerType.ElectromagneticNet:
                    return "电磁网发射器 - 群体控制敌人";
                default:
                    return "未知塔防设施";
            }
        }

        private void OnDestroy()
        {
            EventSystem.Unsubscribe<EnemySpawnEvent>(OnEnemySpawned);
        }
    }
}
