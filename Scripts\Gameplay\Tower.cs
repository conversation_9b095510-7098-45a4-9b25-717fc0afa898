using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace WastelandReclaim
{
    public class Tower : MonoBehaviour
    {
        [Header("Tower Data")]
        public TowerType towerType;
        public int level = 1;
        public Vector2Int gridPosition;

        [Header("Visual")]
        public Renderer towerRenderer;
        public GameObject rangeIndicator;
        public ParticleSystem effectParticles;
        public GameObject upgradeEffect;

        [Header("Audio")]
        public AudioClip activationSound;
        public AudioClip upgradeSound;

        private TowerData towerData;
        private TowerStats currentStats;
        private List<Enemy> enemiesInRange = new List<Enemy>();
        private Enemy currentTarget;
        private float lastAttackTime;
        private AudioSource audioSource;
        private bool isActive = true;

        // Tower effects
        private Coroutine continuousEffectCoroutine;

        public void Initialize(TowerData data)
        {
            towerData = data;
            towerType = data.towerType;
            level = data.level;
            gridPosition = data.position;
            currentStats = data.stats;

            SetupComponents();
            SetupVisuals();
            StartTowerEffects();
        }

        private void SetupComponents()
        {
            if (towerRenderer == null)
                towerRenderer = GetComponent<Renderer>();

            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();

            if (rangeIndicator != null)
                rangeIndicator.SetActive(false);
        }

        private void SetupVisuals()
        {
            // Set tower color based on type
            if (towerRenderer != null)
            {
                Color towerColor = GetTowerColor();
                towerRenderer.material.color = towerColor;
            }

            // Setup range indicator
            if (rangeIndicator != null)
            {
                float range = currentStats.range;
                rangeIndicator.transform.localScale = Vector3.one * range * 2f;
            }

            // Setup particles
            if (effectParticles != null)
            {
                var main = effectParticles.main;
                main.startColor = GetTowerColor();
            }
        }

        private Color GetTowerColor()
        {
            switch (towerType)
            {
                case TowerType.WindFilter:
                    return new Color(0.7f, 0.9f, 1f); // Light blue
                case TowerType.SonicRepeller:
                    return new Color(1f, 0.8f, 0.2f); // Orange
                case TowerType.ShieldGenerator:
                    return new Color(0.2f, 0.8f, 0.2f); // Green
                case TowerType.ResourceAccelerator:
                    return new Color(0.8f, 0.2f, 0.8f); // Purple
                case TowerType.LaserTurret:
                    return new Color(1f, 0.2f, 0.2f); // Red
                case TowerType.ElectromagneticNet:
                    return new Color(0.2f, 0.2f, 1f); // Blue
                default:
                    return Color.white;
            }
        }

        private void StartTowerEffects()
        {
            switch (towerType)
            {
                case TowerType.WindFilter:
                    continuousEffectCoroutine = StartCoroutine(WindFilterEffect());
                    break;
                case TowerType.ShieldGenerator:
                    continuousEffectCoroutine = StartCoroutine(ShieldGeneratorEffect());
                    break;
                case TowerType.ResourceAccelerator:
                    continuousEffectCoroutine = StartCoroutine(ResourceAcceleratorEffect());
                    break;
            }
        }

        public void UpdateTower()
        {
            if (!isActive) return;

            UpdateEnemyDetection();
            UpdateTargeting();
            UpdateAttack();
        }

        private void UpdateEnemyDetection()
        {
            // Find enemies in range
            enemiesInRange.Clear();
            
            EnemySystem enemySystem = FindObjectOfType<EnemySystem>();
            if (enemySystem != null)
            {
                var allEnemies = enemySystem.GetActiveEnemies();
                foreach (var enemy in allEnemies)
                {
                    if (enemy != null)
                    {
                        float distance = Vector3.Distance(transform.position, enemy.transform.position);
                        if (distance <= currentStats.range)
                        {
                            enemiesInRange.Add(enemy);
                        }
                    }
                }
            }
        }

        private void UpdateTargeting()
        {
            // Remove invalid targets
            if (currentTarget == null || !enemiesInRange.Contains(currentTarget))
            {
                currentTarget = null;
            }

            // Find new target if needed
            if (currentTarget == null && enemiesInRange.Count > 0)
            {
                currentTarget = GetBestTarget();
            }
        }

        private Enemy GetBestTarget()
        {
            if (enemiesInRange.Count == 0) return null;

            Enemy bestTarget = null;
            float bestScore = float.MinValue;

            foreach (var enemy in enemiesInRange)
            {
                if (enemy == null) continue;

                float score = CalculateTargetScore(enemy);
                if (score > bestScore)
                {
                    bestScore = score;
                    bestTarget = enemy;
                }
            }

            return bestTarget;
        }

        private float CalculateTargetScore(Enemy enemy)
        {
            float score = 0f;

            // Prioritize by distance (closer = higher score)
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            score += (currentStats.range - distance) * 10f;

            // Prioritize by health (lower health = higher score for finishing off)
            score += (enemy.GetMaxHealth() - enemy.GetCurrentHealth()) * 5f;

            // Prioritize by threat level
            score += enemy.GetPollutionDamage() * 2f;

            return score;
        }

        private void UpdateAttack()
        {
            if (currentTarget == null) return;

            float timeSinceLastAttack = Time.time - lastAttackTime;
            float attackCooldown = 1f / currentStats.attackSpeed;

            if (timeSinceLastAttack >= attackCooldown)
            {
                PerformAttack();
                lastAttackTime = Time.time;
            }
        }

        private void PerformAttack()
        {
            if (currentTarget == null) return;

            switch (towerType)
            {
                case TowerType.SonicRepeller:
                    SonicAttack();
                    break;
                case TowerType.LaserTurret:
                    LaserAttack();
                    break;
                case TowerType.ElectromagneticNet:
                    ElectromagneticAttack();
                    break;
            }

            PlayAttackEffect();
        }

        private void SonicAttack()
        {
            // Apply slow effect to target
            currentTarget.ApplySlowEffect(currentStats.effectDuration, 0.5f);
            
            // Play sonic wave effect
            if (effectParticles != null)
            {
                effectParticles.Play();
            }
        }

        private void LaserAttack()
        {
            // Deal damage to target
            currentTarget.TakeDamage((int)currentStats.damage);
            
            // Create laser beam effect
            StartCoroutine(LaserBeamEffect());
        }

        private void ElectromagneticAttack()
        {
            // Apply electromagnetic effect to all enemies in range
            foreach (var enemy in enemiesInRange)
            {
                if (enemy != null)
                {
                    enemy.ApplyElectromagneticEffect(currentStats.effectDuration);
                }
            }
        }

        private IEnumerator LaserBeamEffect()
        {
            // Create a line renderer for laser beam
            LineRenderer laser = gameObject.GetComponent<LineRenderer>();
            if (laser == null)
            {
                laser = gameObject.AddComponent<LineRenderer>();
                laser.material = new Material(Shader.Find("Sprites/Default"));
                laser.color = Color.red;
                laser.startWidth = 0.1f;
                laser.endWidth = 0.05f;
            }

            laser.enabled = true;
            laser.positionCount = 2;
            laser.SetPosition(0, transform.position);
            laser.SetPosition(1, currentTarget.transform.position);

            yield return new WaitForSeconds(0.1f);

            laser.enabled = false;
        }

        private IEnumerator WindFilterEffect()
        {
            while (isActive)
            {
                // Continuously reduce pollution in area
                GameManager.Instance.ReducePollution(1);
                
                // Play wind effect
                if (effectParticles != null && !effectParticles.isPlaying)
                {
                    effectParticles.Play();
                }

                yield return new WaitForSeconds(1f);
            }
        }

        private IEnumerator ShieldGeneratorEffect()
        {
            while (isActive)
            {
                // Create protective barrier effect
                // This would prevent pollution increase in the protected area
                
                yield return new WaitForSeconds(2f);
            }
        }

        private IEnumerator ResourceAcceleratorEffect()
        {
            while (isActive)
            {
                // Boost resource generation in nearby area
                // This effect would be handled by the match system
                
                yield return new WaitForSeconds(3f);
            }
        }

        private void PlayAttackEffect()
        {
            if (audioSource != null && activationSound != null)
            {
                audioSource.PlayOneShot(activationSound);
            }
        }

        public bool CanUpgrade()
        {
            return level < 3; // Max level 3
        }

        public Dictionary<ResourceType, int> GetUpgradeCost()
        {
            var cost = new Dictionary<ResourceType, int>();
            
            // Base upgrade cost increases with level
            int baseCost = level * 5;
            
            switch (towerType)
            {
                case TowerType.WindFilter:
                    cost[ResourceType.Metal] = baseCost;
                    cost[ResourceType.Core] = level;
                    break;
                case TowerType.SonicRepeller:
                    cost[ResourceType.Electronic] = baseCost / 2;
                    cost[ResourceType.Metal] = baseCost / 2;
                    cost[ResourceType.Core] = level;
                    break;
                case TowerType.ShieldGenerator:
                    cost[ResourceType.Electronic] = baseCost;
                    cost[ResourceType.Glass] = baseCost / 2;
                    cost[ResourceType.Core] = level * 2;
                    break;
                default:
                    cost[ResourceType.Metal] = baseCost;
                    cost[ResourceType.Core] = level;
                    break;
            }
            
            return cost;
        }

        public void Upgrade()
        {
            if (!CanUpgrade()) return;

            level++;
            
            // Improve stats
            currentStats.range *= 1.2f;
            currentStats.damage *= 1.3f;
            currentStats.attackSpeed *= 1.1f;
            currentStats.effectDuration *= 1.2f;

            // Update visuals
            SetupVisuals();
            
            // Play upgrade effect
            if (upgradeEffect != null)
            {
                GameObject effect = Instantiate(upgradeEffect, transform.position, Quaternion.identity);
                Destroy(effect, 2f);
            }

            if (audioSource != null && upgradeSound != null)
            {
                audioSource.PlayOneShot(upgradeSound);
            }

            Debug.Log($"Tower upgraded to level {level}");
        }

        public void OnEnemySpawned(Enemy enemy)
        {
            // React to new enemy spawn if needed
        }

        public void SetActive(bool active)
        {
            isActive = active;
            
            if (!active && continuousEffectCoroutine != null)
            {
                StopCoroutine(continuousEffectCoroutine);
                continuousEffectCoroutine = null;
            }
            else if (active && continuousEffectCoroutine == null)
            {
                StartTowerEffects();
            }
        }

        public void ShowRange(bool show)
        {
            if (rangeIndicator != null)
            {
                rangeIndicator.SetActive(show);
            }
        }

        public int GetLevel()
        {
            return level;
        }

        public TowerStats GetStats()
        {
            return currentStats;
        }

        public TowerType GetTowerType()
        {
            return towerType;
        }

        public Vector2Int GetGridPosition()
        {
            return gridPosition;
        }

        private void OnMouseEnter()
        {
            ShowRange(true);
        }

        private void OnMouseExit()
        {
            ShowRange(false);
        }

        private void OnMouseDown()
        {
            // Handle tower selection/upgrade
            TowerSystem towerSystem = FindObjectOfType<TowerSystem>();
            if (towerSystem != null)
            {
                towerSystem.TryUpgradeTower(gridPosition);
            }
        }

        private void OnDestroy()
        {
            if (continuousEffectCoroutine != null)
            {
                StopCoroutine(continuousEffectCoroutine);
            }
        }

        private void OnDrawGizmosSelected()
        {
            // Draw range in editor
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, currentStats.range);
        }
    }
}
