using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using UnityEngine;

namespace WastelandReclaim
{
    public static class SaveSystem
    {
        private const string SAVE_FILE_NAME = "PlayerSave.dat";
        private const string ENCRYPTION_KEY = "WastelandReclaimKey2024";
        
        private static string SavePath => Path.Combine(Application.persistentDataPath, SAVE_FILE_NAME);

        public static void SavePlayerData(PlayerData playerData)
        {
            try
            {
                // Convert to JSON
                string jsonData = JsonUtility.ToJson(playerData, true);
                
                // Encrypt data
                string encryptedData = EncryptString(jsonData);
                
                // Write to file
                File.WriteAllText(SavePath, encryptedData);
                
                Debug.Log($"Player data saved to: {SavePath}");
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to save player data: {e.Message}");
            }
        }

        public static PlayerData LoadPlayerData()
        {
            try
            {
                if (!File.Exists(SavePath))
                {
                    Debug.Log("No save file found, creating new player data");
                    return CreateDefaultPlayerData();
                }

                // Read encrypted data
                string encryptedData = File.ReadAllText(SavePath);
                
                // Decrypt data
                string jsonData = DecryptString(encryptedData);
                
                // Parse JSON
                PlayerData playerData = JsonUtility.FromJson<PlayerData>(jsonData);
                
                if (playerData == null)
                {
                    Debug.LogWarning("Failed to parse save data, creating new player data");
                    return CreateDefaultPlayerData();
                }

                Debug.Log("Player data loaded successfully");
                return playerData;
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to load player data: {e.Message}");
                return CreateDefaultPlayerData();
            }
        }

        public static bool SaveExists()
        {
            return File.Exists(SavePath);
        }

        public static void DeleteSave()
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    File.Delete(SavePath);
                    Debug.Log("Save file deleted");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to delete save file: {e.Message}");
            }
        }

        public static void BackupSave()
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    string backupPath = SavePath + ".backup";
                    File.Copy(SavePath, backupPath, true);
                    Debug.Log($"Save file backed up to: {backupPath}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to backup save file: {e.Message}");
            }
        }

        public static bool RestoreBackup()
        {
            try
            {
                string backupPath = SavePath + ".backup";
                if (File.Exists(backupPath))
                {
                    File.Copy(backupPath, SavePath, true);
                    Debug.Log("Save file restored from backup");
                    return true;
                }
                else
                {
                    Debug.LogWarning("No backup file found");
                    return false;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to restore backup: {e.Message}");
                return false;
            }
        }

        private static PlayerData CreateDefaultPlayerData()
        {
            var playerData = new PlayerData();
            
            // Set default values
            playerData.playerName = "新玩家";
            playerData.level = 1;
            playerData.experience = 0;
            
            // Starting resources
            playerData.resources[ResourceType.Metal] = 50;
            playerData.resources[ResourceType.Plastic] = 30;
            playerData.resources[ResourceType.Wood] = 20;
            playerData.resources[ResourceType.Core] = 5;
            
            // Initialize base
            playerData.baseData = new BaseData();
            
            // Default settings
            playerData.settings = new SettingsData();
            
            return playerData;
        }

        private static string EncryptString(string plainText)
        {
            try
            {
                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                byte[] keyBytes = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);
                
                // Simple XOR encryption (for demo purposes)
                // In production, use proper encryption like AES
                byte[] encryptedBytes = new byte[plainBytes.Length];
                
                for (int i = 0; i < plainBytes.Length; i++)
                {
                    encryptedBytes[i] = (byte)(plainBytes[i] ^ keyBytes[i % keyBytes.Length]);
                }
                
                return Convert.ToBase64String(encryptedBytes);
            }
            catch (Exception e)
            {
                Debug.LogError($"Encryption failed: {e.Message}");
                return plainText; // Return unencrypted as fallback
            }
        }

        private static string DecryptString(string encryptedText)
        {
            try
            {
                byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
                byte[] keyBytes = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);
                
                // Simple XOR decryption
                byte[] decryptedBytes = new byte[encryptedBytes.Length];
                
                for (int i = 0; i < encryptedBytes.Length; i++)
                {
                    decryptedBytes[i] = (byte)(encryptedBytes[i] ^ keyBytes[i % keyBytes.Length]);
                }
                
                return Encoding.UTF8.GetString(decryptedBytes);
            }
            catch (Exception e)
            {
                Debug.LogError($"Decryption failed: {e.Message}");
                throw; // Re-throw to trigger fallback in LoadPlayerData
            }
        }

        // Cloud save functionality (placeholder for future implementation)
        public static void UploadToCloud(PlayerData playerData)
        {
            // TODO: Implement cloud save upload
            Debug.Log("Cloud save upload not implemented yet");
        }

        public static PlayerData DownloadFromCloud()
        {
            // TODO: Implement cloud save download
            Debug.Log("Cloud save download not implemented yet");
            return null;
        }

        // Settings-specific save/load
        public static void SaveSettings(SettingsData settings)
        {
            try
            {
                string jsonData = JsonUtility.ToJson(settings, true);
                PlayerPrefs.SetString("GameSettings", jsonData);
                PlayerPrefs.Save();
                Debug.Log("Settings saved");
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to save settings: {e.Message}");
            }
        }

        public static SettingsData LoadSettings()
        {
            try
            {
                if (PlayerPrefs.HasKey("GameSettings"))
                {
                    string jsonData = PlayerPrefs.GetString("GameSettings");
                    SettingsData settings = JsonUtility.FromJson<SettingsData>(jsonData);
                    return settings ?? new SettingsData();
                }
                else
                {
                    return new SettingsData();
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to load settings: {e.Message}");
                return new SettingsData();
            }
        }

        // Level progress save/load
        public static void SaveLevelProgress(int levelId, int stars, bool completed)
        {
            try
            {
                PlayerPrefs.SetInt($"Level_{levelId}_Stars", stars);
                PlayerPrefs.SetInt($"Level_{levelId}_Completed", completed ? 1 : 0);
                PlayerPrefs.Save();
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to save level progress: {e.Message}");
            }
        }

        public static (int stars, bool completed) LoadLevelProgress(int levelId)
        {
            try
            {
                int stars = PlayerPrefs.GetInt($"Level_{levelId}_Stars", 0);
                bool completed = PlayerPrefs.GetInt($"Level_{levelId}_Completed", 0) == 1;
                return (stars, completed);
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to load level progress: {e.Message}");
                return (0, false);
            }
        }

        // Statistics tracking
        public static void SaveStatistic(string statName, int value)
        {
            try
            {
                PlayerPrefs.SetInt($"Stat_{statName}", value);
                PlayerPrefs.Save();
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to save statistic {statName}: {e.Message}");
            }
        }

        public static int LoadStatistic(string statName, int defaultValue = 0)
        {
            try
            {
                return PlayerPrefs.GetInt($"Stat_{statName}", defaultValue);
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to load statistic {statName}: {e.Message}");
                return defaultValue;
            }
        }

        // Utility methods
        public static long GetSaveFileSize()
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    FileInfo fileInfo = new FileInfo(SavePath);
                    return fileInfo.Length;
                }
                return 0;
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to get save file size: {e.Message}");
                return 0;
            }
        }

        public static DateTime GetSaveFileDate()
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    return File.GetLastWriteTime(SavePath);
                }
                return DateTime.MinValue;
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to get save file date: {e.Message}");
                return DateTime.MinValue;
            }
        }

        public static bool ValidateSaveFile()
        {
            try
            {
                if (!File.Exists(SavePath))
                    return false;

                // Try to load and parse the save file
                PlayerData testData = LoadPlayerData();
                return testData != null && !string.IsNullOrEmpty(testData.playerId);
            }
            catch (Exception e)
            {
                Debug.LogError($"Save file validation failed: {e.Message}");
                return false;
            }
        }
    }
}
